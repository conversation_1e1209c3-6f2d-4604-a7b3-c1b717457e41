import { FetchHttpHandler } from '@aws-sdk/fetch-http-handler';
import { randomStr } from '@shell/utils/string';
import { isArray, addObjects } from '@shell/utils/array';

export const state = () => {
  return {
    connectivityResults: null
  };
};

class Handler {
  constructor(cloudCredentialId) {
    this.cloudCredentialId = (cloudCredentialId || '');
  }

  handle(httpRequest, ...args) {
    httpRequest.headers['x-api-headers-restrict'] = 'Content-Length';

    if ( this.cloudCredentialId ) {
      httpRequest.headers['x-api-cattleauth-header'] = `awsv4 credID=${ this.cloudCredentialId }`;
    } else {
      httpRequest.headers['x-api-auth-header'] = httpRequest.headers['authorization'];
    }

    delete httpRequest.headers['authorization'];

    httpRequest.headers['content-type'] = `rancher:${ httpRequest.headers['content-type'] }`;

    const endpoint = `/meta/proxy/`;

    if ( !httpRequest.path.startsWith(endpoint) ) {
      httpRequest.path = endpoint + httpRequest.hostname + httpRequest.path;
    }

    httpRequest.protocol = window.location.protocol;
    httpRequest.hostname = window.location.hostname;
    httpRequest.port = window.location.port;

    return FetchHttpHandler.prototype.handle.call(this, httpRequest, ...args);
  }
}

function credentialDefaultProvider(accessKey, secretKey) {
  return function() {
    // The SDK will complain if these aren't set, so fill them with something
    // even though the cloudCredential will be used eventually
    const out = {
      accessKeyId:     accessKey || randomStr(),
      secretAccessKey: secretKey || randomStr(),
    };

    return out;
  };
}

export const getters = {
  connectivityResults(state) {
    return state.connectivityResults;
  }
};

export const mutations = {
  setConnectivityResults(state, results) {
    state.connectivityResults = results;
  }
};

export const actions = {
  ec2Lib() {
    return import(/* webpackChunkName: "aws-ec2" */ '@aws-sdk/client-ec2');
  },

  eksLib() {
    return import(/* webpackChunkName: "aws-eks" */ '@aws-sdk/client-eks');
  },

  kmsLib() {
    return import(/* webpackChunkName: "aws-kms" */ '@aws-sdk/client-kms');
  },

  iamLib() {
    return import(/* webpackChunkName: "aws-iam" */ '@aws-sdk/client-iam');
  },

  async ec2({ dispatch }, {
    region, cloudCredentialId, accessKey, secretKey
  }) {
    const lib = await dispatch('ec2Lib');

    const client = new lib.EC2({
      region,
      credentialDefaultProvider: credentialDefaultProvider(accessKey, secretKey),
      requestHandler:            new Handler(cloudCredentialId),
    });

    return client;
  },

  async eks({ dispatch }, {
    region, cloudCredentialId, accessKey, secretKey
  }) {
    const lib = await dispatch('eksLib');

    const client = new lib.EKS({
      region,
      credentialDefaultProvider: credentialDefaultProvider(accessKey, secretKey),
      requestHandler:            new Handler(cloudCredentialId),
    });

    return client;
  },

  async kms({ dispatch }, {
    region, cloudCredentialId, accessKey, secretKey
  }) {
    const lib = await dispatch('kmsLib');

    const client = new lib.KMS({
      region,
      credentialDefaultProvider: credentialDefaultProvider(accessKey, secretKey),
      requestHandler:            new Handler(cloudCredentialId),
    });

    return client;
  },

  async iam({ dispatch }, {
    region, cloudCredentialId, accessKey, secretKey
  }) {
    const lib = await dispatch('iamLib');

    const client = new lib.IAM({
      region,
      credentialDefaultProvider: credentialDefaultProvider(accessKey, secretKey),
      requestHandler:            new Handler(cloudCredentialId),
    });

    return client;
  },

  async depaginateList(ctx, {
    client, cmd, key, opt
  }) {
    let hasNext = true;
    const out = [];
    let iterations = 0;
    const maxIterations = 100; // Safety limit to prevent infinite loops

    opt = opt || {};

    while ( hasNext && iterations < maxIterations ) {
      iterations++;
      
      const res = await client[cmd](opt);

      if ( !key ) {
        key = Object.keys(res).find((x) => isArray(res[x]));
      }

      addObjects(out, res[key]);
      
      if (res.NextToken) {
        opt.NextToken = res.NextToken;
        hasNext = true;
      } else if (res.Marker) {
        opt.Marker = res.Marker;
        hasNext = true;
      } else {
        hasNext = false;
      }
    }

    if (iterations >= maxIterations) {
      console.warn(`depaginateList: ${cmd} hit max iterations limit`);
    }

    return out;
  },

  async describeInternetGateways({ dispatch }, { client, vpcId }) {
    const filters = vpcId ? [{ Name: 'attachment.vpc-id', Values: [vpcId] }] : [];
    
    return await dispatch('depaginateList', { 
      client, 
      cmd: 'describeInternetGateways',
      opt: { Filters: filters }
    });
  },

  async describeRouteTables({ dispatch }, { client, vpcId, subnetIds }) {
    const filters = [];
    
    if (vpcId) {
      filters.push({ Name: 'vpc-id', Values: [vpcId] });
    }
    
    if (subnetIds && subnetIds.length > 0) {
      filters.push({ Name: 'association.subnet-id', Values: subnetIds });
    }
    
    return await dispatch('depaginateList', { 
      client, 
      cmd: 'describeRouteTables',
      opt: { Filters: filters }
    });
  },

  async validateSubnetConnectivity({ dispatch, commit }, { 
    region, cloudCredentialId, vpcId, subnetIds 
  }) {
    
    // Add timeout wrapper function
    const withTimeout = (promise, timeoutMs = 30000) => {
      return Promise.race([
        promise,
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error(`Operation timed out after ${timeoutMs}ms`)), timeoutMs)
        )
      ]);
    };
    
    const client = await withTimeout(dispatch('ec2', { region, cloudCredentialId }));
    
    try {
      // Get internet gateways for the VPC
      const internetGateways = await withTimeout(dispatch('describeInternetGateways', { client, vpcId }));
      
      // Get route tables for the VPC
      const allRouteTables = await withTimeout(dispatch('describeRouteTables', { client, vpcId }));
      
      // Get route tables specifically associated with our subnets
      const subnetRouteTables = await withTimeout(dispatch('describeRouteTables', { client, vpcId, subnetIds }));
      
      // Find the main route table for the VPC (fallback for subnets without explicit associations)
      const mainRouteTable = allRouteTables.find(rt => 
        rt.Associations?.some(assoc => assoc.Main === true)
      );
      
      const results = {
        hasInternetGateway: internetGateways.length > 0,
        internetGateways,
        subnetResults: []
      };
      
      // Check each subnet's connectivity
      for (const subnetId of subnetIds) {
        // Find the route table for this subnet
        let routeTable = subnetRouteTables.find(rt => 
          rt.Associations?.some(assoc => assoc.SubnetId === subnetId)
        );
        
        // If no explicit association, use the main route table
        if (!routeTable) {
          routeTable = mainRouteTable;
        }
        
        const hasInternetRoute = routeTable?.Routes?.some(route => 
          route.DestinationCidrBlock === '0.0.0.0/0' && 
          route.GatewayId?.startsWith('igw-') &&
          route.State === 'active'
        ) || false;
        
        results.subnetResults.push({
          subnetId,
          hasInternetAccess: hasInternetRoute,
          routeTableId: routeTable?.RouteTableId,
          isUsingMainRouteTable: !subnetRouteTables.find(rt => 
            rt.Associations?.some(assoc => assoc.SubnetId === subnetId)
          )
        });
      }
      
      commit('setConnectivityResults', results);
      return results;
      
    } catch (error) {
      console.error('validateSubnetConnectivity: Error occurred', error);
      
      // Provide more specific error messages
      let errorMessage = 'Failed to validate subnet connectivity';
      
      if (error.message?.includes('timed out')) {
        errorMessage = 'Request timed out while validating subnet connectivity. Please check your network connection and try again.';
      } else if (error.code === 'UnauthorizedOperation' || error.statusCode === 403) {
        errorMessage = 'Insufficient permissions to validate subnet connectivity. Please check your AWS credentials.';
      } else if (error.code === 'InvalidVpcID.NotFound') {
        errorMessage = 'The specified VPC was not found.';
      } else if (error.code === 'InvalidSubnetID.NotFound') {
        errorMessage = 'One or more specified subnets were not found.';
      } else if (error.message) {
        errorMessage = `Failed to validate subnet connectivity: ${error.message}`;
      }
      
      throw new Error(errorMessage);
    }
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};