{"compilerOptions": {"allowJs": true, "target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "baseUrl": ".", "preserveSymlinks": true, "typeRoots": ["../../node_modules", "../../node_modules/@rancher/shell/types", "./types"], "types": ["node", "webpack-env", "@types/node", "@types/jest", "@types/lodash", "rancher", "shell"], "lib": ["esnext", "dom", "dom.iterable", "scripthost"], "paths": {"@shell/*": [".shell/*"], "@components/*": [".rancher-components/src/components/*"]}}, "include": ["**/*.ts", "**/*.d.ts", "**/*.tsx", "**/*.vue"], "exclude": ["../../node_modules"]}