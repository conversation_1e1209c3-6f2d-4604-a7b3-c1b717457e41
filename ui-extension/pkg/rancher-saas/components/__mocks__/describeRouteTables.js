export default {
  $metadata: {
    httpStatusCode: 200,
    requestId: 'rt-12345',
    attempts: 1,
    totalRetryDelay: 0
  },
  RouteTables: [
    // Main route table for vpc-123 with internet gateway route
    {
      RouteTableId: 'rtb-main-123',
      VpcId: 'vpc-123',
      Routes: [
        {
          DestinationCidrBlock: '192.168.0.0/16',
          GatewayId: 'local',
          State: 'active',
          Origin: 'CreateRouteTable'
        },
        {
          DestinationCidrBlock: '0.0.0.0/0',
          GatewayId: 'igw-123456789',
          State: 'active',
          Origin: 'CreateRoute'
        }
      ],
      Associations: [
        {
          RouteTableAssociationId: 'rtbassoc-main-123',
          RouteTableId: 'rtb-main-123',
          Main: true,
          AssociationState: {
            State: 'associated'
          }
        }
      ],
      Tags: [
        {
          Key: 'Name',
          Value: 'main-route-table'
        }
      ]
    },
    // Custom route table for subnet-1234 with internet gateway route
    {
      RouteTableId: 'rtb-custom-1234',
      VpcId: 'vpc-123',
      Routes: [
        {
          DestinationCidrBlock: '192.168.0.0/16',
          GatewayId: 'local',
          State: 'active',
          Origin: 'CreateRouteTable'
        },
        {
          DestinationCidrBlock: '0.0.0.0/0',
          GatewayId: 'igw-123456789',
          State: 'active',
          Origin: 'CreateRoute'
        }
      ],
      Associations: [
        {
          RouteTableAssociationId: 'rtbassoc-custom-1234',
          RouteTableId: 'rtb-custom-1234',
          SubnetId: 'subnet-1234',
          Main: false,
          AssociationState: {
            State: 'associated'
          }
        }
      ],
      Tags: [
        {
          Key: 'Name',
          Value: 'public-route-table'
        }
      ]
    },
    // Private route table for subnet-4321 without internet gateway route
    {
      RouteTableId: 'rtb-private-4321',
      VpcId: 'vpc-123',
      Routes: [
        {
          DestinationCidrBlock: '192.168.0.0/16',
          GatewayId: 'local',
          State: 'active',
          Origin: 'CreateRouteTable'
        }
      ],
      Associations: [
        {
          RouteTableAssociationId: 'rtbassoc-private-4321',
          RouteTableId: 'rtb-private-4321',
          SubnetId: 'subnet-4321',
          Main: false,
          AssociationState: {
            State: 'associated'
          }
        }
      ],
      Tags: [
        {
          Key: 'Name',
          Value: 'private-route-table'
        }
      ]
    },
    // Route table for vpc-1234 (different VPC) 
    {
      RouteTableId: 'rtb-other-vpc',
      VpcId: 'vpc-1234',
      Routes: [
        {
          DestinationCidrBlock: '172.31.0.0/16',
          GatewayId: 'local',
          State: 'active',
          Origin: 'CreateRouteTable'
        },
        {
          DestinationCidrBlock: '0.0.0.0/0',
          GatewayId: 'igw-987654321',
          State: 'active',
          Origin: 'CreateRoute'
        }
      ],
      Associations: [
        {
          RouteTableAssociationId: 'rtbassoc-other-vpc',
          RouteTableId: 'rtb-other-vpc',
          Main: true,
          AssociationState: {
            State: 'associated'
          }
        }
      ],
      Tags: [
        {
          Key: 'Name',
          Value: 'other-vpc-main-rt'
        }
      ]
    },
    // Route table for subnet-12 (no internet access)
    {
      RouteTableId: 'rtb-no-internet',
      VpcId: 'vpc-12345',
      Routes: [
        {
          DestinationCidrBlock: '192.168.0.0/16',
          GatewayId: 'local',
          State: 'active',
          Origin: 'CreateRouteTable'
        }
      ],
      Associations: [
        {
          RouteTableAssociationId: 'rtbassoc-no-internet',
          RouteTableId: 'rtb-no-internet',
          SubnetId: 'subnet-12',
          Main: false,
          AssociationState: {
            State: 'associated'
          }
        }
      ],
      Tags: [
        {
          Key: 'Name',
          Value: 'no-internet-route-table'
        }
      ]
    }
  ]
};