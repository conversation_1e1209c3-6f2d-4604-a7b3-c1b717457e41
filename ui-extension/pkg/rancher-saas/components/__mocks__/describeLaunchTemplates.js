export default {
  $metadata: {
    httpStatusCode:  200,
    requestId:       '12345678',
    attempts:        1,
    totalRetryDelay: 0
  },
  LaunchTemplates: [
    {
      LaunchTemplateId:     'lt-123123',
      LaunchTemplateName:   'rancher-managed-lt-deleteme',
      CreateTime:           '2023-04-21T17:57:41.000Z',
      CreatedBy:            'arn:aws:sts::testdata1234',
      DefaultVersionNumber: 1,
      LatestVersionNumber:  2,
      Tags:                 [
        {
          Key:   'rancher-managed-template',
          Value: 'do-not-modify-or-delete'
        }
      ]
    },
    {
      LaunchTemplateId:     'lt-3123123',
      LaunchTemplateName:   'rancher-managed-lt-nb-eks',
      CreateTime:           '2024-02-29T14:40:49.000Z',
      CreatedBy:            'arn:aws:sts::testdata1234',
      DefaultVersionNumber: 1,
      LatestVersionNumber:  3,
      Tags:                 [
        {
          Key:   'rancher-managed-template',
          Value: 'do-not-modify-or-delete'
        }
      ]
    },
    {
      LaunchTemplateId:     'lt-123123',
      LaunchTemplateName:   'rancher-managed-lt-eks-est',
      CreateTime:           '2023-08-31T11:48:26.000Z',
      CreatedBy:            'arn:aws:sts::testdata1234',
      DefaultVersionNumber: 1,
      LatestVersionNumber:  2,
      Tags:                 [
        {
          Key:   'rancher-managed-template',
          Value: 'do-not-modify-or-delete'
        }
      ]
    },
    {
      LaunchTemplateId:     'lt-123123',
      LaunchTemplateName:   'rancher-managed-lt-nb-defaults-old',
      CreateTime:           '2024-03-05T18:30:54.000Z',
      CreatedBy:            'arn:aws:sts::testdata1234',
      DefaultVersionNumber: 1,
      LatestVersionNumber:  2,
      Tags:                 [
        {
          Key:   'rancher-managed-template',
          Value: 'do-not-modify-or-delete'
        }
      ]
    },
    {
      LaunchTemplateId:     'lt-123123',
      LaunchTemplateName:   'rancher-managed-lt-deletemeactual125',
      CreateTime:           '2023-04-21T18:10:12.000Z',
      CreatedBy:            'arn:aws:sts::testdata1234',
      DefaultVersionNumber: 1,
      LatestVersionNumber:  2,
      Tags:                 [
        {
          Key:   'rancher-managed-template',
          Value: 'do-not-modify-or-delete'
        }
      ]
    },
    {
      LaunchTemplateId:     'lt-123123',
      LaunchTemplateName:   'rancher-managed-lt-rc-eks2',
      CreateTime:           '2023-08-01T16:28:02.000Z',
      CreatedBy:            'arn:aws:sts::testdata1234',
      DefaultVersionNumber: 1,
      LatestVersionNumber:  2,
      Tags:                 [
        {
          Key:   'rancher-managed-template',
          Value: 'do-not-modify-or-delete'
        }
      ]
    },
    {
      LaunchTemplateId:     'lt-123123',
      LaunchTemplateName:   'rancher-managed-lt-fromrancher2',
      CreateTime:           '2023-04-21T17:03:34.000Z',
      CreatedBy:            'arn:aws:sts::testdata1234',
      DefaultVersionNumber: 1,
      LatestVersionNumber:  2,
      Tags:                 [
        {
          Key:   'rancher-managed-template',
          Value: 'do-not-modify-or-delete'
        }
      ]
    },
    {
      LaunchTemplateId:     'lt-1234',
      LaunchTemplateName:   'nb-template',
      CreateTime:           '2024-02-29T22:25:05.000Z',
      CreatedBy:            'arn:aws:sts::testdata1234',
      DefaultVersionNumber: 3,
      LatestVersionNumber:  4
    },
    {
      LaunchTemplateId:     'lt-123123',
      LaunchTemplateName:   'rancher-managed-lt-mo-eks-test-2',
      CreateTime:           '2024-01-15T19:28:31.000Z',
      CreatedBy:            'arn:aws:sts::testdata1234',
      DefaultVersionNumber: 1,
      LatestVersionNumber:  2,
      Tags:                 [
        {
          Key:   'rancher-managed-template',
          Value: 'do-not-modify-or-delete'
        }
      ]
    },
    {
      LaunchTemplateId:     'lt-123123',
      LaunchTemplateName:   'rancher-managed-lt-rc-eks',
      CreateTime:           '2023-07-21T16:16:45.000Z',
      CreatedBy:            'arn:aws:sts::testdata1234',
      DefaultVersionNumber: 1,
      LatestVersionNumber:  2,
      Tags:                 [
        {
          Key:   'rancher-managed-template',
          Value: 'do-not-modify-or-delete'
        }
      ]
    },
    {
      LaunchTemplateId:     'lt-123123',
      LaunchTemplateName:   'rancher-managed-lt-nb-old-defaults',
      CreateTime:           '2024-03-06T20:50:39.000Z',
      CreatedBy:            'arn:aws:sts::testdata1234',
      DefaultVersionNumber: 1,
      LatestVersionNumber:  2,
      Tags:                 [
        {
          Key:   'rancher-managed-template',
          Value: 'do-not-modify-or-delete'
        }
      ]
    },
    {
      LaunchTemplateId:     'lt-123123',
      LaunchTemplateName:   'rancher-managed-lt-nb-eks-test2',
      CreateTime:           '2024-03-05T21:47:01.000Z',
      CreatedBy:            'arn:aws:sts::testdata1234',
      DefaultVersionNumber: 1,
      LatestVersionNumber:  2,
      Tags:                 [
        {
          Key:   'rancher-managed-template',
          Value: 'do-not-modify-or-delete'
        }
      ]
    },
    {
      LaunchTemplateId:     'lt-123123',
      LaunchTemplateName:   'rancher-managed-lt-nb-duplicate-names',
      CreateTime:           '2024-03-06T12:25:56.000Z',
      CreatedBy:            'arn:aws:sts::testdata1234',
      DefaultVersionNumber: 1,
      LatestVersionNumber:  35,
      Tags:                 [
        {
          Key:   'rancher-managed-template',
          Value: 'do-not-modify-or-delete'
        }
      ]
    },
    {
      LaunchTemplateId:     'lt-123123',
      LaunchTemplateName:   'rancher-managed-lt-eks-test',
      CreateTime:           '2024-01-17T22:14:17.000Z',
      CreatedBy:            'arn:aws:sts::testdata1234',
      DefaultVersionNumber: 1,
      LatestVersionNumber:  2,
      Tags:                 [
        {
          Key:   'rancher-managed-template',
          Value: 'do-not-modify-or-delete'
        }
      ]
    },
    {
      LaunchTemplateId:     'lt-123123',
      LaunchTemplateName:   'rancher-managed-lt-mo-test-ebs',
      CreateTime:           '2024-01-16T21:36:36.000Z',
      CreatedBy:            'arn:aws:sts::testdata1234',
      DefaultVersionNumber: 1,
      LatestVersionNumber:  2,
      Tags:                 [
        {
          Key:   'rancher-managed-template',
          Value: 'do-not-modify-or-delete'
        }
      ]
    },
    {
      LaunchTemplateId:     'lt-123123',
      LaunchTemplateName:   'test-template',
      CreateTime:           '2024-03-01T17:41:32.000Z',
      CreatedBy:            'arn:aws:sts::testdata1234',
      DefaultVersionNumber: 1,
      LatestVersionNumber:  4
    },
    {
      LaunchTemplateId:     'lt-123123',
      LaunchTemplateName:   'rancher-managed-lt-nb-eks-size',
      CreateTime:           '2024-03-06T13:27:48.000Z',
      CreatedBy:            'arn:aws:sts::testdata1234',
      DefaultVersionNumber: 1,
      LatestVersionNumber:  58,
      Tags:                 [
        {
          Key:   'rancher-managed-template',
          Value: 'do-not-modify-or-delete'
        }
      ]
    },
    {
      LaunchTemplateId:     'lt-123123123',
      LaunchTemplateName:   'rancher-managed-lt-mo-eks-test-3',
      CreateTime:           '2024-01-15T19:45:06.000Z',
      CreatedBy:            'arn:aws:sts::testdata1234',
      DefaultVersionNumber: 1,
      LatestVersionNumber:  2,
      Tags:                 [
        {
          Key:   'rancher-managed-template',
          Value: 'do-not-modify-or-delete'
        }
      ]
    },
    {
      LaunchTemplateId:     'lt-123123',
      LaunchTemplateName:   'rancher-managed-lt-mo-eks',
      CreateTime:           '2023-08-11T16:04:09.000Z',
      CreatedBy:            'arn:aws:sts::testdata1234',
      DefaultVersionNumber: 1,
      LatestVersionNumber:  2,
      Tags:                 [
        {
          Key:   'rancher-managed-template',
          Value: 'do-not-modify-or-delete'
        }
      ]
    },
    {
      LaunchTemplateId:     'lt-12312312',
      LaunchTemplateName:   'rancher-managed-lt-mo-eks-test',
      CreateTime:           '2024-01-15T18:28:48.000Z',
      CreatedBy:            'arn:aws:sts::testdata1234',
      DefaultVersionNumber: 1,
      LatestVersionNumber:  2,
      Tags:                 [
        {
          Key:   'rancher-managed-template',
          Value: 'do-not-modify-or-delete'
        }
      ]
    },
    {
      LaunchTemplateId:     'lt-1231232',
      LaunchTemplateName:   'rancher-managed-lt-nb-eks-old',
      CreateTime:           '2024-03-06T22:41:26.000Z',
      CreatedBy:            'arn:aws:sts::testdata1234',
      DefaultVersionNumber: 1,
      LatestVersionNumber:  2,
      Tags:                 [
        {
          Key:   'rancher-managed-template',
          Value: 'do-not-modify-or-delete'
        }
      ]
    }
  ]
};
