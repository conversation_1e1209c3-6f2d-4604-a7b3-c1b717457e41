export default {
  $metadata: {
    httpStatusCode:  200,
    requestId:       '113ab499-e00c-4197-a6d5-d33b30e53c54',
    attempts:        1,
    totalRetryDelay: 0
  },
  addons: [
    {
      addonName:     'vpc-cni',
      addonVersions: [
        {
          addonVersion: 'v1.16.4-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '2.0',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.16.3-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.16.2-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.16.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.15.5-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.15.4-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.15.3-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.15.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.15.0-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.14.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.14.0-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.13.4-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.13.3-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.13.2-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.13.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.12.6-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.12.6-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.12.5-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.12.5-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.12.2-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.12.1-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.12.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.12.0-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.12.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.11.5-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.11.4-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.11.4-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.11.4-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.11.3-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.11.3-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.11.3-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.11.2-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.11.2-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.11.2-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.11.0-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.11.0-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.11.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.10.4-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.10.4-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.10.4-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.10.3-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.10.3-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.10.3-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.10.2-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.10.2-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.10.2-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.10.1-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.10.1-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.10.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.10.0-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.10.0-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.10.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.9.3-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.9.3-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.9.3-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.9.1-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.9.1-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.9.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.9.0-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.9.0-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.9.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.8.0-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.8.0-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.8.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.7.10-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.7.10-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.7.9-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.7.9-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.7.6-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.7.6-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.7.5-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.7.5-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.6.3-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.6.3-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'networking'
    },
    {
      addonName:     'upwind-security_upwind-operator',
      addonVersions: [
        {
          addonVersion: 'v0.3.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'security'
    },
    {
      addonName:     'upbound_universal-crossplane',
      addonVersions: [
        {
          addonVersion: 'v1.9.1-eksbuild.0',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'infra-management'
    },
    {
      addonName:     'tetrate-io_istio-distro',
      addonVersions: [
        {
          addonVersion: 'v1.18.0-eksbuild.1',
          architecture: [
            'amd64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.16.1-eksbuild.0',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.15.3-eksbuild.0',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'policy-management'
    },
    {
      addonName:     'teleport_teleport',
      addonVersions: [
        {
          addonVersion: 'v10.3.1-eksbuild.0',
          architecture: [
            'amd64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'policy-management'
    },
    {
      addonName:     'stormforge_optimize-live',
      addonVersions: [
        {
          addonVersion: 'v2.9.1-eksbuild.1',
          architecture: [
            'amd64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v2.8.0-eksbuild.1',
          architecture: [
            'amd64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'cost-management'
    },
    {
      addonName:     'splunk_splunk-otel-collector-chart',
      addonVersions: [
        {
          addonVersion: 'v0.86.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'monitoring'
    },
    {
      addonName:     'solo-io_istio-distro',
      addonVersions: [
        {
          addonVersion: 'v1.18.3-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'service-mesh'
    },
    {
      addonName:     'snapshot-controller',
      addonVersions: [
        {
          addonVersion: 'v6.3.2-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v6.2.2-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'storage'
    },
    {
      addonName:     'rafay-systems_rafay-operator',
      addonVersions: [
        {
          addonVersion: 'v1.0.6-eksbuild.1',
          architecture: [
            'amd64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'kubernetes-management'
    },
    {
      addonName:     'new-relic_kubernetes-operator',
      addonVersions: [
        {
          addonVersion: 'v0.1.9-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.1.8-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'observability'
    },
    {
      addonName:     'netapp_trident-operator',
      addonVersions: [
        {
          addonVersion: 'v23.10.0-eksbuild.1',
          architecture: [
            'arm64',
            'amd64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'storage'
    },
    {
      addonName:     'leaksignal_leakagent',
      addonVersions: [
        {
          addonVersion: 'v0.7.3-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'monitoring'
    },
    {
      addonName:     'kubecost_kubecost',
      addonVersions: [
        {
          addonVersion: 'v1.103.3-eksbuild.0',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.102.2-eksbuild.0',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.101.3-eksbuild.0',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.100.1-eksbuild.0',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.99.0-eksbuild.0',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.98.0-eksbuild.1',
          architecture: [
            'amd64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'cost-management'
    },
    {
      addonName:     'kube-proxy',
      addonVersions: [
        {
          addonVersion: 'v1.29.1-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.29.0-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.29.0-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.29.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.28.6-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.28.4-eksbuild.4',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.28.4-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.28.2-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.28.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.27.10-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.27.8-eksbuild.4',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.27.8-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.27.6-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.27.4-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.27.3-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.27.3-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.27.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.26.13-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.26.11-eksbuild.4',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.26.11-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.26.9-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.26.7-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.26.6-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.26.6-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.26.4-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.26.2-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.25.16-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.25.16-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.25.16-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.25.15-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.25.14-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.25.11-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.25.11-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.25.9-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.25.6-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.25.6-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.24.17-eksbuild.8',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.24.17-eksbuild.4',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.24.17-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.24.17-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.24.15-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.24.15-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.24.10-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.24.9-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.24.7-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.23.17-eksbuild.9',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.23.17-eksbuild.5',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.23.17-eksbuild.4',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.23.17-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.23.17-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.23.17-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.23.16-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.23.15-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.23.13-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.23.8-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.23.7-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.22.17-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.22.16-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.22.15-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.22.11-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.22.6-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.21.14-eksbuild.4',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.21.14-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.21.14-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.21.2-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.20.15-eksbuild.4',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.20.15-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.20.7-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.20.4-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.19.16-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.19.8-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.19.6-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.18.8-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'networking'
    },
    {
      addonName:     'kong_konnect-ri',
      addonVersions: [
        {
          addonVersion: 'v3.4.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'ingress-service-type'
    },
    {
      addonName:     'kasten_k10',
      addonVersions: [
        {
          addonVersion: 'v5.5.7-eksbuild.0',
          architecture: [
            'amd64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'data-protection'
    },
    {
      addonName:     'haproxy-technologies_kubernetes-ingress-ee',
      addonVersions: [
        {
          addonVersion: 'v1.30.0-eksbuild.0',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'ingress-controller'
    },
    {
      addonName:     'groundcover_agent',
      addonVersions: [
        {
          addonVersion: 'v1.5.27-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'monitoring'
    },
    {
      addonName:     'grafana-labs_kubernetes-monitoring',
      addonVersions: [
        {
          addonVersion: 'v0.6.1-eksbuild.1',
          architecture: [
            'amd64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'monitoring'
    },
    {
      addonName:     'factorhouse_kpow',
      addonVersions: [
        {
          addonVersion: 'v90.2.3-eksbuild.0',
          architecture: [
            'amd64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'monitoring'
    },
    {
      addonName:     'eks-pod-identity-agent',
      addonVersions: [
        {
          addonVersion: 'v1.2.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.1.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.0.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'security'
    },
    {
      addonName:     'dynatrace_dynatrace-operator',
      addonVersions: [
        {
          addonVersion: 'v0.14.2-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.8.2-eksbuild.0',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'monitoring'
    },
    {
      addonName:     'datree_engine-pro',
      addonVersions: [
        {
          addonVersion: 'v1.0.3-eksbuild.0',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'policy-management'
    },
    {
      addonName:     'datadog_operator',
      addonVersions: [
        {
          addonVersion: 'v0.1.5-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'monitoring'
    },
    {
      addonName:     'cribl_cribledge',
      addonVersions: [
        {
          addonVersion: 'v4.3.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'observability'
    },
    {
      addonName:     'coredns',
      addonVersions: [
        {
          addonVersion: 'v1.11.1-eksbuild.6',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.11.1-eksbuild.4',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.11.1-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.10.1-eksbuild.7',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.10.1-eksbuild.6',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.10.1-eksbuild.5',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.10.1-eksbuild.4',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.10.1-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.10.1-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.10.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.9.3-eksbuild.11',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.9.3-eksbuild.10',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.9.3-eksbuild.9',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.9.3-eksbuild.7',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.9.3-eksbuild.6',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.9.3-eksbuild.5',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.9.3-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.9.3-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.8.7-eksbuild.10',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.8.7-eksbuild.9',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.8.7-eksbuild.8',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.8.7-eksbuild.7',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.8.7-eksbuild.6',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.8.7-eksbuild.5',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.8.7-eksbuild.4',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.8.7-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.8.7-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.8.7-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.8.4-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.8.4-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.8.3-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.8.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'networking'
    },
    {
      addonName:     'calyptia_fluent-bit',
      addonVersions: [
        {
          addonVersion: 'v22.10.5-eksbuild.0',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'observability'
    },
    {
      addonName:     'aws-mountpoint-s3-csi-driver',
      addonVersions: [
        {
          addonVersion: 'v1.3.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.3.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.2.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.1.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.0.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'storage'
    },
    {
      addonName:     'aws-guardduty-agent',
      addonVersions: [
        {
          addonVersion: 'v1.5.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.4.1-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.4.0-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.4.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.3.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.3.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.2.0-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.2.0-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.2.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.1.0-eksbuild.1',
          architecture: [
            'amd64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.0.0-eksbuild.1',
          architecture: [
            'amd64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'security'
    },
    {
      addonName:     'aws-efs-csi-driver',
      addonVersions: [
        {
          addonVersion: 'v1.7.5-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.7.5-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.7.4-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.7.3-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.7.2-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.7.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.7.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.5.9-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.5.8-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'storage'
    },
    {
      addonName:     'aws-ebs-csi-driver',
      addonVersions: [
        {
          addonVersion: 'v1.28.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.27.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.26.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.26.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.25.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.24.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.24.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.23.2-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.23.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.23.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.22.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.22.0-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.21.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.20.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.19.0-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.19.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.18.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.17.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.16.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.16.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.15.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.15.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.14.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.14.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.13.0-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.13.0-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.13.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.12.1-eksbuild.3',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.12.1-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.12.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.11.5-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.11.5-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.11.4-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                'eks.9+'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                'eks.7+'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.11.2-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                'eks.9+'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                'eks.7+'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.10.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                'eks.9+'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                'eks.7+'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.9.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                'eks.9+'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                'eks.7+'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.8.0-eksbuild.0',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                'eks.5+'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                'eks.4+'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.7.0-eksbuild.0',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                'eks.5+'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                'eks.4+'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.6.2-eksbuild.0',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                'eks.5+'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                'eks.4+'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.6.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                'eks.5+'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                'eks.4+'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.6.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                'eks.5+'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                'eks.4+'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.5.3-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                'eks.5+'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                'eks.4+'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.5.2-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                'eks.5+'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                'eks.4+'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.4.0-eksbuild.preview',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                'eks.3+'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                'eks.3+'
              ]
            }
          ]
        }
      ],
      type: 'storage'
    },
    {
      addonName:     'amazon-cloudwatch-observability',
      addonVersions: [
        {
          addonVersion: 'v1.2.2-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.2.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.2.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v1.1.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'observability'
    },
    {
      addonName:     'adot',
      addonVersions: [
        {
          addonVersion: 'v0.92.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.29',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.28',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.90.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.88.0-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.88.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.84.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.82.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.80.0-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.80.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.78.0-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.78.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.28',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.76.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.74.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.70.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.66.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.26',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.62.1-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.25',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.62.1-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.61.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.58.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.56.0-eksbuild.2',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.56.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.51.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.45.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.22',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.21',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.20',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        },
        {
          addonVersion: 'v0.29.0-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.24',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   false,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'observability'
    },
    {
      addonName:     'accuknox_kubearmor',
      addonVersions: [
        {
          addonVersion: 'v0.10.2-eksbuild.1',
          architecture: [
            'amd64',
            'arm64'
          ],
          compatibilities: [
            {
              clusterVersion:   '1.27',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.26',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.25',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.24',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            },
            {
              clusterVersion:   '1.23',
              defaultVersion:   true,
              platformVersions: [
                '*'
              ]
            }
          ]
        }
      ],
      type: 'security'
    }
  ]
};
