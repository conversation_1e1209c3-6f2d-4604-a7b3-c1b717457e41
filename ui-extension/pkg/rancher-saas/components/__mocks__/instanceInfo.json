[{"apiName": "c5.large", "currentGeneration": true, "groupLabel": "C5 - High CPU (Intel)", "instanceClass": "c5", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5.large: 2 vCPUs / 4 GiB Memory / EBS-Only"}, {"apiName": "c5.xlarge", "currentGeneration": true, "groupLabel": "C5 - High CPU (Intel)", "instanceClass": "c5", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5.xlarge: 4 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "c5.2xlarge", "currentGeneration": true, "groupLabel": "C5 - High CPU (Intel)", "instanceClass": "c5", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5.2xlarge: 8 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "c5.4xlarge", "currentGeneration": true, "groupLabel": "C5 - High CPU (Intel)", "instanceClass": "c5", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5.4xlarge: 16 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "c5.9xlarge", "currentGeneration": true, "groupLabel": "C5 - High CPU (Intel)", "instanceClass": "c5", "memoryBytes": 77309411328, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5.9xlarge: 36 vCPUs / 72 GiB Memory / EBS-Only"}, {"apiName": "c5.12xlarge", "currentGeneration": true, "groupLabel": "C5 - High CPU (Intel)", "instanceClass": "c5", "memoryBytes": 103079215104, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5.12xlarge: 48 vCPUs / 96 GiB Memory / EBS-Only"}, {"apiName": "c5.18xlarge", "currentGeneration": true, "groupLabel": "C5 - High CPU (Intel)", "instanceClass": "c5", "memoryBytes": 154618822656, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5.18xlarge: 72 vCPUs / 144 GiB Memory / EBS-Only"}, {"apiName": "c5.24xlarge", "currentGeneration": true, "groupLabel": "C5 - High CPU (Intel)", "instanceClass": "c5", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5.24xlarge: 96 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "c5.metal", "currentGeneration": true, "groupLabel": "C5 - High CPU (Intel)", "instanceClass": "c5", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5.metal: 96 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "c5a.large", "currentGeneration": true, "groupLabel": "C5a - High CPU (AMD)", "instanceClass": "c5a", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5a.large: 2 vCPUs / 4 GiB Memory / EBS-Only"}, {"apiName": "c5a.xlarge", "currentGeneration": true, "groupLabel": "C5a - High CPU (AMD)", "instanceClass": "c5a", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5a.xlarge: 4 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "c5a.2xlarge", "currentGeneration": true, "groupLabel": "C5a - High CPU (AMD)", "instanceClass": "c5a", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5a.2xlarge: 8 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "c5a.4xlarge", "currentGeneration": true, "groupLabel": "C5a - High CPU (AMD)", "instanceClass": "c5a", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5a.4xlarge: 16 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "c5a.8xlarge", "currentGeneration": true, "groupLabel": "C5a - High CPU (AMD)", "instanceClass": "c5a", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5a.8xlarge: 32 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "c5a.12xlarge", "currentGeneration": true, "groupLabel": "C5a - High CPU (AMD)", "instanceClass": "c5a", "memoryBytes": 103079215104, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5a.12xlarge: 48 vCPUs / 96 GiB Memory / EBS-Only"}, {"apiName": "c5a.16xlarge", "currentGeneration": true, "groupLabel": "C5a - High CPU (AMD)", "instanceClass": "c5a", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5a.16xlarge: 64 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "c5a.24xlarge", "currentGeneration": true, "groupLabel": "C5a - High CPU (AMD)", "instanceClass": "c5a", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5a.24xlarge: 96 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "c5ad.large", "currentGeneration": true, "groupLabel": "C5ad - High CPU (AMD, Local SSD)", "instanceClass": "c5ad", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5ad.large: 2 vCPUs / 4 GiB Memory / 75 GB SSD"}, {"apiName": "c5ad.xlarge", "currentGeneration": true, "groupLabel": "C5ad - High CPU (AMD, Local SSD)", "instanceClass": "c5ad", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5ad.xlarge: 4 vCPUs / 8 GiB Memory / 150 GB SSD"}, {"apiName": "c5ad.2xlarge", "currentGeneration": true, "groupLabel": "C5ad - High CPU (AMD, Local SSD)", "instanceClass": "c5ad", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5ad.2xlarge: 8 vCPUs / 16 GiB Memory / 300 GB SSD"}, {"apiName": "c5ad.4xlarge", "currentGeneration": true, "groupLabel": "C5ad - High CPU (AMD, Local SSD)", "instanceClass": "c5ad", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5ad.4xlarge: 16 vCPUs / 32 GiB Memory / 600 GB SSD"}, {"apiName": "c5ad.8xlarge", "currentGeneration": true, "groupLabel": "C5ad - High CPU (AMD, Local SSD)", "instanceClass": "c5ad", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5ad.8xlarge: 32 vCPUs / 64 GiB Memory / 1.2 TB SSD"}, {"apiName": "c5ad.12xlarge", "currentGeneration": true, "groupLabel": "C5ad - High CPU (AMD, Local SSD)", "instanceClass": "c5ad", "memoryBytes": 103079215104, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5ad.12xlarge: 48 vCPUs / 96 GiB Memory / 1.8 TB SSD"}, {"apiName": "c5ad.16xlarge", "currentGeneration": true, "groupLabel": "C5ad - High CPU (AMD, Local SSD)", "instanceClass": "c5ad", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5ad.16xlarge: 64 vCPUs / 128 GiB Memory / 2.4 TB SSD"}, {"apiName": "c5ad.24xlarge", "currentGeneration": true, "groupLabel": "C5ad - High CPU (AMD, Local SSD)", "instanceClass": "c5ad", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5ad.24xlarge: 96 vCPUs / 192 GiB Memory / 3.8 TB SSD"}, {"apiName": "c5d.large", "currentGeneration": true, "groupLabel": "C5d - High CPU (Intel, Local SSD)", "instanceClass": "c5d", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5d.large: 2 vCPUs / 4 GiB Memory / 50 GB SSD"}, {"apiName": "c5d.xlarge", "currentGeneration": true, "groupLabel": "C5d - High CPU (Intel, Local SSD)", "instanceClass": "c5d", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5d.xlarge: 4 vCPUs / 8 GiB Memory / 100 GB SSD"}, {"apiName": "c5d.2xlarge", "currentGeneration": true, "groupLabel": "C5d - High CPU (Intel, Local SSD)", "instanceClass": "c5d", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5d.2xlarge: 8 vCPUs / 16 GiB Memory / 200 GB SSD"}, {"apiName": "c5d.4xlarge", "currentGeneration": true, "groupLabel": "C5d - High CPU (Intel, Local SSD)", "instanceClass": "c5d", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5d.4xlarge: 16 vCPUs / 32 GiB Memory / 400 GB SSD"}, {"apiName": "c5d.9xlarge", "currentGeneration": true, "groupLabel": "C5d - High CPU (Intel, Local SSD)", "instanceClass": "c5d", "memoryBytes": 77309411328, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5d.9xlarge: 36 vCPUs / 72 GiB Memory / 900 GB SSD"}, {"apiName": "c5d.12xlarge", "currentGeneration": true, "groupLabel": "C5d - High CPU (Intel, Local SSD)", "instanceClass": "c5d", "memoryBytes": 103079215104, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5d.12xlarge: 48 vCPUs / 96 GiB Memory / 1.8 TB SSD"}, {"apiName": "c5d.18xlarge", "currentGeneration": true, "groupLabel": "C5d - High CPU (Intel, Local SSD)", "instanceClass": "c5d", "memoryBytes": 154618822656, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5d.18xlarge: 72 vCPUs / 144 GiB Memory / 1.8 TB SSD"}, {"apiName": "c5d.24xlarge", "currentGeneration": true, "groupLabel": "C5d - High CPU (Intel, Local SSD)", "instanceClass": "c5d", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5d.24xlarge: 96 vCPUs / 192 GiB Memory / 3.6 TB SSD"}, {"apiName": "c5d.metal", "currentGeneration": true, "groupLabel": "C5d - High CPU (Intel, Local SSD)", "instanceClass": "c5d", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5d.metal: 96 vCPUs / 192 GiB Memory / 3.6 TB SSD"}, {"apiName": "c5n.large", "currentGeneration": true, "groupLabel": "C5n - High CPU (Intel)", "instanceClass": "c5n", "memoryBytes": 5637144576, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5n.large: 2 vCPUs / 5.25 GiB Memory / EBS-Only"}, {"apiName": "c5n.xlarge", "currentGeneration": true, "groupLabel": "C5n - High CPU (Intel)", "instanceClass": "c5n", "memoryBytes": 11274289152, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5n.xlarge: 4 vCPUs / 10.5 GiB Memory / EBS-Only"}, {"apiName": "c5n.2xlarge", "currentGeneration": true, "groupLabel": "C5n - High CPU (Intel)", "instanceClass": "c5n", "memoryBytes": 22548578304, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5n.2xlarge: 8 vCPUs / 21 GiB Memory / EBS-Only"}, {"apiName": "c5n.4xlarge", "currentGeneration": true, "groupLabel": "C5n - High CPU (Intel)", "instanceClass": "c5n", "memoryBytes": 45097156608, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5n.4xlarge: 16 vCPUs / 42 GiB Memory / EBS-Only"}, {"apiName": "c5n.9xlarge", "currentGeneration": true, "groupLabel": "C5n - High CPU (Intel)", "instanceClass": "c5n", "memoryBytes": 103079215104, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5n.9xlarge: 36 vCPUs / 96 GiB Memory / EBS-Only"}, {"apiName": "c5n.18xlarge", "currentGeneration": true, "groupLabel": "C5n - High CPU (Intel)", "instanceClass": "c5n", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5n.18xlarge: 72 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "c5n.metal", "currentGeneration": true, "groupLabel": "C5n - High CPU (Intel)", "instanceClass": "c5n", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "c5n.metal: 72 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "c6g.medium", "currentGeneration": true, "groupLabel": "C6g - High CPU (ARM)", "instanceClass": "c6g", "memoryBytes": 2147483648, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6g.medium: 1 vCPU / 2 GiB Memory / EBS-Only"}, {"apiName": "c6g.large", "currentGeneration": true, "groupLabel": "C6g - High CPU (ARM)", "instanceClass": "c6g", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6g.large: 2 vCPUs / 4 GiB Memory / EBS-Only"}, {"apiName": "c6g.xlarge", "currentGeneration": true, "groupLabel": "C6g - High CPU (ARM)", "instanceClass": "c6g", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6g.xlarge: 4 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "c6g.2xlarge", "currentGeneration": true, "groupLabel": "C6g - High CPU (ARM)", "instanceClass": "c6g", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6g.2xlarge: 8 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "c6g.4xlarge", "currentGeneration": true, "groupLabel": "C6g - High CPU (ARM)", "instanceClass": "c6g", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6g.4xlarge: 16 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "c6g.8xlarge", "currentGeneration": true, "groupLabel": "C6g - High CPU (ARM)", "instanceClass": "c6g", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6g.8xlarge: 32 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "c6g.12xlarge", "currentGeneration": true, "groupLabel": "C6g - High CPU (ARM)", "instanceClass": "c6g", "memoryBytes": 103079215104, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6g.12xlarge: 48 vCPUs / 96 GiB Memory / EBS-Only"}, {"apiName": "c6g.16xlarge", "currentGeneration": true, "groupLabel": "C6g - High CPU (ARM)", "instanceClass": "c6g", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6g.16xlarge: 64 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "c6g.metal", "currentGeneration": true, "groupLabel": "C6g - High CPU (ARM)", "instanceClass": "c6g", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6g.metal: 64 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "c6gd.medium", "currentGeneration": true, "groupLabel": "C6gd - High CPU (ARM, Local SSD)", "instanceClass": "c6gd", "memoryBytes": 2147483648, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6gd.medium: 1 vCPU / 2 GiB Memory / 59 GB SSD"}, {"apiName": "c6gd.large", "currentGeneration": true, "groupLabel": "C6gd - High CPU (ARM, Local SSD)", "instanceClass": "c6gd", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6gd.large: 2 vCPUs / 4 GiB Memory / 118 GB SSD"}, {"apiName": "c6gd.xlarge", "currentGeneration": true, "groupLabel": "C6gd - High CPU (ARM, Local SSD)", "instanceClass": "c6gd", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6gd.xlarge: 4 vCPUs / 8 GiB Memory / 237 GB SSD"}, {"apiName": "c6gd.2xlarge", "currentGeneration": true, "groupLabel": "C6gd - High CPU (ARM, Local SSD)", "instanceClass": "c6gd", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6gd.2xlarge: 8 vCPUs / 16 GiB Memory / 474 GB SSD"}, {"apiName": "c6gd.4xlarge", "currentGeneration": true, "groupLabel": "C6gd - High CPU (ARM, Local SSD)", "instanceClass": "c6gd", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6gd.4xlarge: 16 vCPUs / 32 GiB Memory / 950 GB SSD"}, {"apiName": "c6gd.8xlarge", "currentGeneration": true, "groupLabel": "C6gd - High CPU (ARM, Local SSD)", "instanceClass": "c6gd", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6gd.8xlarge: 32 vCPUs / 64 GiB Memory / 1.9 TB SSD"}, {"apiName": "c6gd.12xlarge", "currentGeneration": true, "groupLabel": "C6gd - High CPU (ARM, Local SSD)", "instanceClass": "c6gd", "memoryBytes": 103079215104, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6gd.12xlarge: 48 vCPUs / 96 GiB Memory / 2.85 TB SSD"}, {"apiName": "c6gd.16xlarge", "currentGeneration": true, "groupLabel": "C6gd - High CPU (ARM, Local SSD)", "instanceClass": "c6gd", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6gd.16xlarge: 64 vCPUs / 128 GiB Memory / 3.8 TB SSD"}, {"apiName": "c6gd.metal", "currentGeneration": true, "groupLabel": "C6gd - High CPU (ARM, Local SSD)", "instanceClass": "c6gd", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6gd.metal: 64 vCPUs / 128 GiB Memory / 3.8 TB SSD"}, {"apiName": "c6gn.medium", "currentGeneration": true, "groupLabel": "C6gn - High CPU (ARM)", "instanceClass": "c6gn", "memoryBytes": 2147483648, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6gn.medium: 1 vCPU / 2 GiB Memory / EBS-Only"}, {"apiName": "c6gn.large", "currentGeneration": true, "groupLabel": "C6gn - High CPU (ARM)", "instanceClass": "c6gn", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6gn.large: 2 vCPUs / 4 GiB Memory / EBS-Only"}, {"apiName": "c6gn.xlarge", "currentGeneration": true, "groupLabel": "C6gn - High CPU (ARM)", "instanceClass": "c6gn", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6gn.xlarge: 4 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "c6gn.2xlarge", "currentGeneration": true, "groupLabel": "C6gn - High CPU (ARM)", "instanceClass": "c6gn", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6gn.2xlarge: 8 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "c6gn.4xlarge", "currentGeneration": true, "groupLabel": "C6gn - High CPU (ARM)", "instanceClass": "c6gn", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6gn.4xlarge: 16 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "c6gn.8xlarge", "currentGeneration": true, "groupLabel": "C6gn - High CPU (ARM)", "instanceClass": "c6gn", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6gn.8xlarge: 32 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "c6gn.12xlarge", "currentGeneration": true, "groupLabel": "C6gn - High CPU (ARM)", "instanceClass": "c6gn", "memoryBytes": 103079215104, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6gn.12xlarge: 48 vCPUs / 96 GiB Memory / EBS-Only"}, {"apiName": "c6gn.16xlarge", "currentGeneration": true, "groupLabel": "C6gn - High CPU (ARM)", "instanceClass": "c6gn", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6gn.16xlarge: 64 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "c6i.large", "currentGeneration": true, "groupLabel": "C6i - High-CPU (Intel)", "instanceClass": "c6i", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6i.large: 2 vCPUs / 4 GiB Memory / EBS-Only"}, {"apiName": "c6i.xlarge", "currentGeneration": true, "groupLabel": "C6i - High-CPU (Intel)", "instanceClass": "c6i", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6i.xlarge: 4 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "c6i.2xlarge", "currentGeneration": true, "groupLabel": "C6i - High-CPU (Intel)", "instanceClass": "c6i", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6i.2xlarge: 8 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "c6i.4xlarge", "currentGeneration": true, "groupLabel": "C6i - High-CPU (Intel)", "instanceClass": "c6i", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6i.4xlarge: 16 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "c6i.8xlarge", "currentGeneration": true, "groupLabel": "C6i - High-CPU (Intel)", "instanceClass": "c6i", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6i.8xlarge: 32 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "c6i.12xlarge", "currentGeneration": true, "groupLabel": "C6i - High-CPU (Intel)", "instanceClass": "c6i", "memoryBytes": 103079215104, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6i.12xlarge: 48 vCPUs / 96 GiB Memory / EBS-Only"}, {"apiName": "c6i.16xlarge", "currentGeneration": true, "groupLabel": "C6i - High-CPU (Intel)", "instanceClass": "c6i", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6i.16xlarge: 64 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "c6i.24xlarge", "currentGeneration": true, "groupLabel": "C6i - High-CPU (Intel)", "instanceClass": "c6i", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6i.24xlarge: 96 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "c6i.32xlarge", "currentGeneration": true, "groupLabel": "C6i - High-CPU (Intel)", "instanceClass": "c6i", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6i.32xlarge: 128 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "c6i.metal", "currentGeneration": true, "groupLabel": "C6i - High-CPU (Intel)", "instanceClass": "c6i", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6i.metal: 128 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "d3.xlarge", "currentGeneration": true, "groupLabel": "D3 - High Density Storage (Local HDD)", "instanceClass": "d3", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "d3.xlarge: 4 vCPUs / 32 GiB Memory / 5.94 TB HDD"}, {"apiName": "d3.2xlarge", "currentGeneration": true, "groupLabel": "D3 - High Density Storage (Local HDD)", "instanceClass": "d3", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "d3.2xlarge: 8 vCPUs / 64 GiB Memory / 11.88 TB HDD"}, {"apiName": "d3.4xlarge", "currentGeneration": true, "groupLabel": "D3 - High Density Storage (Local HDD)", "instanceClass": "d3", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "d3.4xlarge: 16 vCPUs / 128 GiB Memory / 23.76 TB HDD"}, {"apiName": "d3.8xlarge", "currentGeneration": true, "groupLabel": "D3 - High Density Storage (Local HDD)", "instanceClass": "d3", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "d3.8xlarge: 32 vCPUs / 256 GiB Memory / 47.52 TB HDD"}, {"apiName": "d3en.xlarge", "currentGeneration": true, "groupLabel": "D3en - High Density Storage (Local HDD)", "instanceClass": "d3en", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "d3en.xlarge: 4 vCPUs / 16 GiB Memory / 27.96 TB HDD"}, {"apiName": "d3en.2xlarge", "currentGeneration": true, "groupLabel": "D3en - High Density Storage (Local HDD)", "instanceClass": "d3en", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "d3en.2xlarge: 8 vCPUs / 32 GiB Memory / 55.92 TB HDD"}, {"apiName": "d3en.4xlarge", "currentGeneration": true, "groupLabel": "D3en - High Density Storage (Local HDD)", "instanceClass": "d3en", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "d3en.4xlarge: 16 vCPUs / 64 GiB Memory / 111.84 TB HDD"}, {"apiName": "d3en.6xlarge", "currentGeneration": true, "groupLabel": "D3en - High Density Storage (Local HDD)", "instanceClass": "d3en", "memoryBytes": 103079215104, "supportedUsageClasses": ["on-demand", "spot"], "label": "d3en.6xlarge: 24 vCPUs / 96 GiB Memory / 167.76 TB HDD"}, {"apiName": "d3en.8xlarge", "currentGeneration": true, "groupLabel": "D3en - High Density Storage (Local HDD)", "instanceClass": "d3en", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "d3en.8xlarge: 32 vCPUs / 128 GiB Memory / 223.68 TB HDD"}, {"apiName": "d3en.12xlarge", "currentGeneration": true, "groupLabel": "D3en - High Density Storage (Local HDD)", "instanceClass": "d3en", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "d3en.12xlarge: 48 vCPUs / 192 GiB Memory / 335.52 TB HDD"}, {"apiName": "dl1.24xlarge", "currentGeneration": true, "groupLabel": "DL1 - ML Accelerator (Intel, Gaudi)", "instanceClass": "dl1", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "dl1.24xlarge: 96 vCPUs / 768 GiB Memory / 4 TB SSD"}, {"apiName": "f1.2xlarge", "currentGeneration": true, "groupLabel": "F1 - FPGA (Local SSD)", "instanceClass": "f1", "memoryBytes": 130996502528, "supportedUsageClasses": ["on-demand", "spot"], "label": "f1.2xlarge: 8 vCPUs / 122 GiB Memory / 470 GB SSD"}, {"apiName": "f1.4xlarge", "currentGeneration": true, "groupLabel": "F1 - FPGA (Local SSD)", "instanceClass": "f1", "memoryBytes": 261993005056, "supportedUsageClasses": ["on-demand", "spot"], "label": "f1.4xlarge: 16 vCPUs / 244 GiB Memory / 940 GB SSD"}, {"apiName": "f1.16xlarge", "currentGeneration": true, "groupLabel": "F1 - FPGA (Local SSD)", "instanceClass": "f1", "memoryBytes": 1047972020224, "supportedUsageClasses": ["on-demand", "spot"], "label": "f1.16xlarge: 64 vCPUs / 976 GiB Memory / 3.76 TB SSD"}, {"apiName": "g3.4xlarge", "currentGeneration": true, "groupLabel": "G3 - GPU (NVIDIA M60)", "instanceClass": "g3", "memoryBytes": 130996502528, "supportedUsageClasses": ["on-demand", "spot"], "label": "g3.4xlarge: 16 vCPUs / 122 GiB Memory / EBS-Only"}, {"apiName": "g3.8xlarge", "currentGeneration": true, "groupLabel": "G3 - GPU (NVIDIA M60)", "instanceClass": "g3", "memoryBytes": 261993005056, "supportedUsageClasses": ["on-demand", "spot"], "label": "g3.8xlarge: 32 vCPUs / 244 GiB Memory / EBS-Only"}, {"apiName": "g3.16xlarge", "currentGeneration": true, "groupLabel": "G3 - GPU (NVIDIA M60)", "instanceClass": "g3", "memoryBytes": 523986010112, "supportedUsageClasses": ["on-demand", "spot"], "label": "g3.16xlarge: 64 vCPUs / 488 GiB Memory / EBS-Only"}, {"apiName": "g3s.xlarge", "currentGeneration": true, "groupLabel": "G3s - GPU (NVIDIA M60)", "instanceClass": "g3s", "memoryBytes": 32749125632, "supportedUsageClasses": ["on-demand", "spot"], "label": "g3s.xlarge: 4 vCPUs / 30.5 GiB Memory / EBS-Only"}, {"apiName": "g4ad.xlarge", "currentGeneration": true, "groupLabel": "G4ad - GPU (AMD, Radeon V520, Local SSD)", "instanceClass": "g4ad", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "g4ad.xlarge: 4 vCPUs / 16 GiB Memory / 150 GB SSD"}, {"apiName": "g4ad.2xlarge", "currentGeneration": true, "groupLabel": "G4ad - GPU (AMD, Radeon V520, Local SSD)", "instanceClass": "g4ad", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "g4ad.2xlarge: 8 vCPUs / 32 GiB Memory / 300 GB SSD"}, {"apiName": "g4ad.4xlarge", "currentGeneration": true, "groupLabel": "G4ad - GPU (AMD, Radeon V520, Local SSD)", "instanceClass": "g4ad", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "g4ad.4xlarge: 16 vCPUs / 64 GiB Memory / 600 GB SSD"}, {"apiName": "g4ad.8xlarge", "currentGeneration": true, "groupLabel": "G4ad - GPU (AMD, Radeon V520, Local SSD)", "instanceClass": "g4ad", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "g4ad.8xlarge: 32 vCPUs / 128 GiB Memory / 1.2 TB SSD"}, {"apiName": "g4ad.16xlarge", "currentGeneration": true, "groupLabel": "G4ad - GPU (AMD, Radeon V520, Local SSD)", "instanceClass": "g4ad", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "g4ad.16xlarge: 64 vCPUs / 256 GiB Memory / 2.4 TB SSD"}, {"apiName": "g4dn.xlarge", "currentGeneration": true, "groupLabel": "G4dn - GPU (Intel, NVIDIA T4, Local SSD)", "instanceClass": "g4dn", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "g4dn.xlarge: 4 vCPUs / 16 GiB Memory / 125 GB SSD"}, {"apiName": "g4dn.2xlarge", "currentGeneration": true, "groupLabel": "G4dn - GPU (Intel, NVIDIA T4, Local SSD)", "instanceClass": "g4dn", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "g4dn.2xlarge: 8 vCPUs / 32 GiB Memory / 225 GB SSD"}, {"apiName": "g4dn.4xlarge", "currentGeneration": true, "groupLabel": "G4dn - GPU (Intel, NVIDIA T4, Local SSD)", "instanceClass": "g4dn", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "g4dn.4xlarge: 16 vCPUs / 64 GiB Memory / 225 GB SSD"}, {"apiName": "g4dn.8xlarge", "currentGeneration": true, "groupLabel": "G4dn - GPU (Intel, NVIDIA T4, Local SSD)", "instanceClass": "g4dn", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "g4dn.8xlarge: 32 vCPUs / 128 GiB Memory / 900 GB SSD"}, {"apiName": "g4dn.12xlarge", "currentGeneration": true, "groupLabel": "G4dn - GPU (Intel, NVIDIA T4, Local SSD)", "instanceClass": "g4dn", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "g4dn.12xlarge: 48 vCPUs / 192 GiB Memory / 900 GB SSD"}, {"apiName": "g4dn.16xlarge", "currentGeneration": true, "groupLabel": "G4dn - GPU (Intel, NVIDIA T4, Local SSD)", "instanceClass": "g4dn", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "g4dn.16xlarge: 64 vCPUs / 256 GiB Memory / 900 GB SSD"}, {"apiName": "g4dn.metal", "currentGeneration": true, "groupLabel": "G4dn - GPU (Intel, NVIDIA T4, Local SSD)", "instanceClass": "g4dn", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "g4dn.metal: 96 vCPUs / 384 GiB Memory / 1.8 TB SSD"}, {"apiName": "g5.xlarge", "currentGeneration": true, "groupLabel": "G5 - GPU (AMD, NVIDIA A10G, Local NVMe)", "instanceClass": "g5", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "g5.xlarge: 4 vCPUs / 16 GiB Memory / 250 GB SSD"}, {"apiName": "g5.2xlarge", "currentGeneration": true, "groupLabel": "G5 - GPU (AMD, NVIDIA A10G, Local NVMe)", "instanceClass": "g5", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "g5.2xlarge: 8 vCPUs / 32 GiB Memory / 450 GB SSD"}, {"apiName": "g5.4xlarge", "currentGeneration": true, "groupLabel": "G5 - GPU (AMD, NVIDIA A10G, Local NVMe)", "instanceClass": "g5", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "g5.4xlarge: 16 vCPUs / 64 GiB Memory / 600 GB SSD"}, {"apiName": "g5.8xlarge", "currentGeneration": true, "groupLabel": "G5 - GPU (AMD, NVIDIA A10G, Local NVMe)", "instanceClass": "g5", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "g5.8xlarge: 32 vCPUs / 128 GiB Memory / 900 GB SSD"}, {"apiName": "g5.12xlarge", "currentGeneration": true, "groupLabel": "G5 - GPU (AMD, NVIDIA A10G, Local NVMe)", "instanceClass": "g5", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "g5.12xlarge: 48 vCPUs / 192 GiB Memory / 3.8 TB SSD"}, {"apiName": "g5.16xlarge", "currentGeneration": true, "groupLabel": "G5 - GPU (AMD, NVIDIA A10G, Local NVMe)", "instanceClass": "g5", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "g5.16xlarge: 64 vCPUs / 256 GiB Memory / 1.9 TB SSD"}, {"apiName": "g5.24xlarge", "currentGeneration": true, "groupLabel": "G5 - GPU (AMD, NVIDIA A10G, Local NVMe)", "instanceClass": "g5", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "g5.24xlarge: 96 vCPUs / 384 GiB Memory / 3.8 TB SSD"}, {"apiName": "g5.48xlarge", "currentGeneration": true, "groupLabel": "G5 - GPU (AMD, NVIDIA A10G, Local NVMe)", "instanceClass": "g5", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "g5.48xlarge: 192 vCPUs / 768 GiB Memory / 7.6 TB SSD"}, {"apiName": "g5g.xlarge", "currentGeneration": true, "groupLabel": "G5g - GPU (ARM, NVIDIA T4G)", "instanceClass": "g5g", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "g5g.xlarge: 4 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "g5g.2xlarge", "currentGeneration": true, "groupLabel": "G5g - GPU (ARM, NVIDIA T4G)", "instanceClass": "g5g", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "g5g.2xlarge: 8 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "g5g.4xlarge", "currentGeneration": true, "groupLabel": "G5g - GPU (ARM, NVIDIA T4G)", "instanceClass": "g5g", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "g5g.4xlarge: 16 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "g5g.8xlarge", "currentGeneration": true, "groupLabel": "G5g - GPU (ARM, NVIDIA T4G)", "instanceClass": "g5g", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "g5g.8xlarge: 32 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "g5g.16xlarge", "currentGeneration": true, "groupLabel": "G5g - GPU (ARM, NVIDIA T4G)", "instanceClass": "g5g", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "g5g.16xlarge: 64 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "g5g.metal", "currentGeneration": true, "groupLabel": "G5g - GPU (ARM, NVIDIA T4G)", "instanceClass": "g5g", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "g5g.metal: 64 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "h1.2xlarge", "currentGeneration": true, "groupLabel": "H1 - High Density Storage (Local HDD)", "instanceClass": "h1", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "h1.2xlarge: 8 vCPUs / 32 GiB Memory / 2 TB HDD"}, {"apiName": "h1.4xlarge", "currentGeneration": true, "groupLabel": "H1 - High Density Storage (Local HDD)", "instanceClass": "h1", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "h1.4xlarge: 16 vCPUs / 64 GiB Memory / 4 TB HDD"}, {"apiName": "h1.8xlarge", "currentGeneration": true, "groupLabel": "H1 - High Density Storage (Local HDD)", "instanceClass": "h1", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "h1.8xlarge: 32 vCPUs / 128 GiB Memory / 8 TB HDD"}, {"apiName": "h1.16xlarge", "currentGeneration": true, "groupLabel": "H1 - High Density Storage (Local HDD)", "instanceClass": "h1", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "h1.16xlarge: 64 vCPUs / 256 GiB Memory / 16 TB HDD"}, {"apiName": "i3.large", "currentGeneration": true, "groupLabel": "I3 - High I/O Storage (Local SSD)", "instanceClass": "i3", "memoryBytes": 16374562816, "supportedUsageClasses": ["on-demand", "spot"], "label": "i3.large: 2 vCPUs / 15.25 GiB Memory / 475 GB SSD"}, {"apiName": "i3.xlarge", "currentGeneration": true, "groupLabel": "I3 - High I/O Storage (Local SSD)", "instanceClass": "i3", "memoryBytes": 32749125632, "supportedUsageClasses": ["on-demand", "spot"], "label": "i3.xlarge: 4 vCPUs / 30.5 GiB Memory / 950 GB SSD"}, {"apiName": "i3.2xlarge", "currentGeneration": true, "groupLabel": "I3 - High I/O Storage (Local SSD)", "instanceClass": "i3", "memoryBytes": 65498251264, "supportedUsageClasses": ["on-demand", "spot"], "label": "i3.2xlarge: 8 vCPUs / 61 GiB Memory / 1.9 TB SSD"}, {"apiName": "i3.4xlarge", "currentGeneration": true, "groupLabel": "I3 - High I/O Storage (Local SSD)", "instanceClass": "i3", "memoryBytes": 130996502528, "supportedUsageClasses": ["on-demand", "spot"], "label": "i3.4xlarge: 16 vCPUs / 122 GiB Memory / 3.8 TB SSD"}, {"apiName": "i3.8xlarge", "currentGeneration": true, "groupLabel": "I3 - High I/O Storage (Local SSD)", "instanceClass": "i3", "memoryBytes": 261993005056, "supportedUsageClasses": ["on-demand", "spot"], "label": "i3.8xlarge: 32 vCPUs / 244 GiB Memory / 7.6 TB SSD"}, {"apiName": "i3.16xlarge", "currentGeneration": true, "groupLabel": "I3 - High I/O Storage (Local SSD)", "instanceClass": "i3", "memoryBytes": 523986010112, "supportedUsageClasses": ["on-demand", "spot"], "label": "i3.16xlarge: 64 vCPUs / 488 GiB Memory / 15.2 TB SSD"}, {"apiName": "i3.metal", "currentGeneration": true, "groupLabel": "I3 - High I/O Storage (Local SSD)", "instanceClass": "i3", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "i3.metal: 72 vCPUs / 512 GiB Memory / 15.2 TB SSD"}, {"apiName": "i3en.large", "currentGeneration": true, "groupLabel": "I3en - High I/O Storage (Local SSD)", "instanceClass": "i3en", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "i3en.large: 2 vCPUs / 16 GiB Memory / 1.25 TB SSD"}, {"apiName": "i3en.xlarge", "currentGeneration": true, "groupLabel": "I3en - High I/O Storage (Local SSD)", "instanceClass": "i3en", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "i3en.xlarge: 4 vCPUs / 32 GiB Memory / 2.5 TB SSD"}, {"apiName": "i3en.2xlarge", "currentGeneration": true, "groupLabel": "I3en - High I/O Storage (Local SSD)", "instanceClass": "i3en", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "i3en.2xlarge: 8 vCPUs / 64 GiB Memory / 5 TB SSD"}, {"apiName": "i3en.3xlarge", "currentGeneration": true, "groupLabel": "I3en - High I/O Storage (Local SSD)", "instanceClass": "i3en", "memoryBytes": 103079215104, "supportedUsageClasses": ["on-demand", "spot"], "label": "i3en.3xlarge: 12 vCPUs / 96 GiB Memory / 7.5 TB SSD"}, {"apiName": "i3en.6xlarge", "currentGeneration": true, "groupLabel": "I3en - High I/O Storage (Local SSD)", "instanceClass": "i3en", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "i3en.6xlarge: 24 vCPUs / 192 GiB Memory / 15 TB SSD"}, {"apiName": "i3en.12xlarge", "currentGeneration": true, "groupLabel": "I3en - High I/O Storage (Local SSD)", "instanceClass": "i3en", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "i3en.12xlarge: 48 vCPUs / 384 GiB Memory / 30 TB SSD"}, {"apiName": "i3en.24xlarge", "currentGeneration": true, "groupLabel": "I3en - High I/O Storage (Local SSD)", "instanceClass": "i3en", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "i3en.24xlarge: 96 vCPUs / 768 GiB Memory / 60 TB SSD"}, {"apiName": "i3en.metal", "currentGeneration": true, "groupLabel": "I3en - High I/O Storage (Local SSD)", "instanceClass": "i3en", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "i3en.metal: 96 vCPUs / 768 GiB Memory / 60 TB SSD"}, {"apiName": "i4i.large", "currentGeneration": true, "groupLabel": "I4i - High I/O Storage (Local Nitro SSD)", "instanceClass": "i4i", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "i4i.large: 2 vCPUs / 16 GiB Memory / 468 GB SSD"}, {"apiName": "i4i.xlarge", "currentGeneration": true, "groupLabel": "I4i - High I/O Storage (Local Nitro SSD)", "instanceClass": "i4i", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "i4i.xlarge: 4 vCPUs / 32 GiB Memory / 937 GB SSD"}, {"apiName": "i4i.2xlarge", "currentGeneration": true, "groupLabel": "I4i - High I/O Storage (Local Nitro SSD)", "instanceClass": "i4i", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "i4i.2xlarge: 8 vCPUs / 64 GiB Memory / 1.875 TB SSD"}, {"apiName": "i4i.4xlarge", "currentGeneration": true, "groupLabel": "I4i - High I/O Storage (Local Nitro SSD)", "instanceClass": "i4i", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "i4i.4xlarge: 16 vCPUs / 128 GiB Memory / 3.75 TB SSD"}, {"apiName": "i4i.8xlarge", "currentGeneration": true, "groupLabel": "I4i - High I/O Storage (Local Nitro SSD)", "instanceClass": "i4i", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "i4i.8xlarge: 32 vCPUs / 256 GiB Memory / 7.5 TB SSD"}, {"apiName": "i4i.12xlarge", "currentGeneration": true, "groupLabel": "I4i - High I/O Storage (Local Nitro SSD)", "instanceClass": "i4i", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "i4i.12xlarge: 48 vCPUs / 384 GiB Memory / 11.25 TB SSD"}, {"apiName": "i4i.16xlarge", "currentGeneration": true, "groupLabel": "I4i - High I/O Storage (Local Nitro SSD)", "instanceClass": "i4i", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "i4i.16xlarge: 64 vCPUs / 512 GiB Memory / 15 TB SSD"}, {"apiName": "i4i.24xlarge", "currentGeneration": true, "groupLabel": "I4i - High I/O Storage (Local Nitro SSD)", "instanceClass": "i4i", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "i4i.24xlarge: 96 vCPUs / 768 GiB Memory / 22.5 TB SSD"}, {"apiName": "i4i.32xlarge", "currentGeneration": true, "groupLabel": "I4i - High I/O Storage (Local Nitro SSD)", "instanceClass": "i4i", "memoryBytes": 1099511627776, "supportedUsageClasses": ["on-demand", "spot"], "label": "i4i.32xlarge: 128 vCPUs / 1,024 GiB Memory / 30 TB SSD"}, {"apiName": "i4i.metal", "currentGeneration": true, "groupLabel": "I4i - High I/O Storage (Local Nitro SSD)", "instanceClass": "i4i", "memoryBytes": 1099511627776, "supportedUsageClasses": ["on-demand", "spot"], "label": "i4i.metal: 128 vCPUs / 1,024 GiB Memory / 30 TB SSD"}, {"apiName": "im4gn.large", "currentGeneration": true, "groupLabel": "Im4gn - Storage (ARM, Local NVME)", "instanceClass": "im4gn", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "im4gn.large: 2 vCPUs / 8 GiB Memory / 937 GB SSD"}, {"apiName": "im4gn.xlarge", "currentGeneration": true, "groupLabel": "Im4gn - Storage (ARM, Local NVME)", "instanceClass": "im4gn", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "im4gn.xlarge: 4 vCPUs / 16 GiB Memory / 1.875 TB SSD"}, {"apiName": "im4gn.2xlarge", "currentGeneration": true, "groupLabel": "Im4gn - Storage (ARM, Local NVME)", "instanceClass": "im4gn", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "im4gn.2xlarge: 8 vCPUs / 32 GiB Memory / 3.75 TB SSD"}, {"apiName": "im4gn.4xlarge", "currentGeneration": true, "groupLabel": "Im4gn - Storage (ARM, Local NVME)", "instanceClass": "im4gn", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "im4gn.4xlarge: 16 vCPUs / 64 GiB Memory / 7.5 TB SSD"}, {"apiName": "im4gn.8xlarge", "currentGeneration": true, "groupLabel": "Im4gn - Storage (ARM, Local NVME)", "instanceClass": "im4gn", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "im4gn.8xlarge: 32 vCPUs / 128 GiB Memory / 15 TB SSD"}, {"apiName": "im4gn.16xlarge", "currentGeneration": true, "groupLabel": "Im4gn - Storage (ARM, Local NVME)", "instanceClass": "im4gn", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "im4gn.16xlarge: 64 vCPUs / 256 GiB Memory / 30 TB SSD"}, {"apiName": "inf1.xlarge", "currentGeneration": true, "groupLabel": "Inf1 - ML Accelerator (Intel, Inferentia)", "instanceClass": "inf1", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "inf1.xlarge: 4 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "inf1.2xlarge", "currentGeneration": true, "groupLabel": "Inf1 - ML Accelerator (Intel, Inferentia)", "instanceClass": "inf1", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "inf1.2xlarge: 8 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "inf1.6xlarge", "currentGeneration": true, "groupLabel": "Inf1 - ML Accelerator (Intel, Inferentia)", "instanceClass": "inf1", "memoryBytes": 51539607552, "supportedUsageClasses": ["on-demand", "spot"], "label": "inf1.6xlarge: 24 vCPUs / 48 GiB Memory / EBS-Only"}, {"apiName": "inf1.24xlarge", "currentGeneration": true, "groupLabel": "Inf1 - ML Accelerator (Intel, Inferentia)", "instanceClass": "inf1", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "inf1.24xlarge: 96 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "m5.large", "currentGeneration": true, "groupLabel": "M5 - General Purpose (Intel)", "instanceClass": "m5", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5.large: 2 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "m5.xlarge", "currentGeneration": true, "groupLabel": "M5 - General Purpose (Intel)", "instanceClass": "m5", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5.xlarge: 4 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "m5.2xlarge", "currentGeneration": true, "groupLabel": "M5 - General Purpose (Intel)", "instanceClass": "m5", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5.2xlarge: 8 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "m5.4xlarge", "currentGeneration": true, "groupLabel": "M5 - General Purpose (Intel)", "instanceClass": "m5", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5.4xlarge: 16 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "m5.8xlarge", "currentGeneration": true, "groupLabel": "M5 - General Purpose (Intel)", "instanceClass": "m5", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5.8xlarge: 32 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "m5.12xlarge", "currentGeneration": true, "groupLabel": "M5 - General Purpose (Intel)", "instanceClass": "m5", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5.12xlarge: 48 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "m5.16xlarge", "currentGeneration": true, "groupLabel": "M5 - General Purpose (Intel)", "instanceClass": "m5", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5.16xlarge: 64 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "m5.24xlarge", "currentGeneration": true, "groupLabel": "M5 - General Purpose (Intel)", "instanceClass": "m5", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5.24xlarge: 96 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "m5.metal", "currentGeneration": true, "groupLabel": "M5 - General Purpose (Intel)", "instanceClass": "m5", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5.metal: 96 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "m5a.large", "currentGeneration": true, "groupLabel": "M5a - General <PERSON> (AMD)", "instanceClass": "m5a", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5a.large: 2 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "m5a.xlarge", "currentGeneration": true, "groupLabel": "M5a - General <PERSON> (AMD)", "instanceClass": "m5a", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5a.xlarge: 4 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "m5a.2xlarge", "currentGeneration": true, "groupLabel": "M5a - General <PERSON> (AMD)", "instanceClass": "m5a", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5a.2xlarge: 8 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "m5a.4xlarge", "currentGeneration": true, "groupLabel": "M5a - General <PERSON> (AMD)", "instanceClass": "m5a", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5a.4xlarge: 16 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "m5a.8xlarge", "currentGeneration": true, "groupLabel": "M5a - General <PERSON> (AMD)", "instanceClass": "m5a", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5a.8xlarge: 32 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "m5a.12xlarge", "currentGeneration": true, "groupLabel": "M5a - General <PERSON> (AMD)", "instanceClass": "m5a", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5a.12xlarge: 48 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "m5a.16xlarge", "currentGeneration": true, "groupLabel": "M5a - General <PERSON> (AMD)", "instanceClass": "m5a", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5a.16xlarge: 64 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "m5a.24xlarge", "currentGeneration": true, "groupLabel": "M5a - General <PERSON> (AMD)", "instanceClass": "m5a", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5a.24xlarge: 96 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "m5ad.large", "currentGeneration": true, "groupLabel": "M5ad - General Purpose (AMD, Local SSD)", "instanceClass": "m5ad", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5ad.large: 2 vCPUs / 8 GiB Memory / 75 GB SSD"}, {"apiName": "m5ad.xlarge", "currentGeneration": true, "groupLabel": "M5ad - General Purpose (AMD, Local SSD)", "instanceClass": "m5ad", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5ad.xlarge: 4 vCPUs / 16 GiB Memory / 150 GB SSD"}, {"apiName": "m5ad.2xlarge", "currentGeneration": true, "groupLabel": "M5ad - General Purpose (AMD, Local SSD)", "instanceClass": "m5ad", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5ad.2xlarge: 8 vCPUs / 32 GiB Memory / 300 GB SSD"}, {"apiName": "m5ad.4xlarge", "currentGeneration": true, "groupLabel": "M5ad - General Purpose (AMD, Local SSD)", "instanceClass": "m5ad", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5ad.4xlarge: 16 vCPUs / 64 GiB Memory / 600 GB SSD"}, {"apiName": "m5ad.8xlarge", "currentGeneration": true, "groupLabel": "M5ad - General Purpose (AMD, Local SSD)", "instanceClass": "m5ad", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5ad.8xlarge: 32 vCPUs / 128 GiB Memory / 1.2 TB SSD"}, {"apiName": "m5ad.12xlarge", "currentGeneration": true, "groupLabel": "M5ad - General Purpose (AMD, Local SSD)", "instanceClass": "m5ad", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5ad.12xlarge: 48 vCPUs / 192 GiB Memory / 1.8 TB SSD"}, {"apiName": "m5ad.16xlarge", "currentGeneration": true, "groupLabel": "M5ad - General Purpose (AMD, Local SSD)", "instanceClass": "m5ad", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5ad.16xlarge: 64 vCPUs / 256 GiB Memory / 2.4 TB SSD"}, {"apiName": "m5ad.24xlarge", "currentGeneration": true, "groupLabel": "M5ad - General Purpose (AMD, Local SSD)", "instanceClass": "m5ad", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5ad.24xlarge: 96 vCPUs / 384 GiB Memory / 3.6 TB SSD"}, {"apiName": "m5d.large", "currentGeneration": true, "groupLabel": "M5d - General Purpose (Intel, Local SSD)", "instanceClass": "m5d", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5d.large: 2 vCPUs / 8 GiB Memory / 75 GB SSD"}, {"apiName": "m5d.xlarge", "currentGeneration": true, "groupLabel": "M5d - General Purpose (Intel, Local SSD)", "instanceClass": "m5d", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5d.xlarge: 4 vCPUs / 16 GiB Memory / 150 GB SSD"}, {"apiName": "m5d.2xlarge", "currentGeneration": true, "groupLabel": "M5d - General Purpose (Intel, Local SSD)", "instanceClass": "m5d", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5d.2xlarge: 8 vCPUs / 32 GiB Memory / 300 GB SSD"}, {"apiName": "m5d.4xlarge", "currentGeneration": true, "groupLabel": "M5d - General Purpose (Intel, Local SSD)", "instanceClass": "m5d", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5d.4xlarge: 16 vCPUs / 64 GiB Memory / 600 GB SSD"}, {"apiName": "m5d.8xlarge", "currentGeneration": true, "groupLabel": "M5d - General Purpose (Intel, Local SSD)", "instanceClass": "m5d", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5d.8xlarge: 32 vCPUs / 128 GiB Memory / 1.2 TB SSD"}, {"apiName": "m5d.12xlarge", "currentGeneration": true, "groupLabel": "M5d - General Purpose (Intel, Local SSD)", "instanceClass": "m5d", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5d.12xlarge: 48 vCPUs / 192 GiB Memory / 1.8 TB SSD"}, {"apiName": "m5d.16xlarge", "currentGeneration": true, "groupLabel": "M5d - General Purpose (Intel, Local SSD)", "instanceClass": "m5d", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5d.16xlarge: 64 vCPUs / 256 GiB Memory / 2.4 TB SSD"}, {"apiName": "m5d.24xlarge", "currentGeneration": true, "groupLabel": "M5d - General Purpose (Intel, Local SSD)", "instanceClass": "m5d", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5d.24xlarge: 96 vCPUs / 384 GiB Memory / 3.6 TB SSD"}, {"apiName": "m5d.metal", "currentGeneration": true, "groupLabel": "M5d - General Purpose (Intel, Local SSD)", "instanceClass": "m5d", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5d.metal: 96 vCPUs / 384 GiB Memory / 3.6 TB SSD"}, {"apiName": "m5dn.large", "currentGeneration": true, "groupLabel": "M5dn - General Purpose (Intel, Local SSD)", "instanceClass": "m5dn", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5dn.large: 2 vCPUs / 8 GiB Memory / 75 GB SSD"}, {"apiName": "m5dn.xlarge", "currentGeneration": true, "groupLabel": "M5dn - General Purpose (Intel, Local SSD)", "instanceClass": "m5dn", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5dn.xlarge: 4 vCPUs / 16 GiB Memory / 150 GB SSD"}, {"apiName": "m5dn.2xlarge", "currentGeneration": true, "groupLabel": "M5dn - General Purpose (Intel, Local SSD)", "instanceClass": "m5dn", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5dn.2xlarge: 8 vCPUs / 32 GiB Memory / 300 GB SSD"}, {"apiName": "m5dn.4xlarge", "currentGeneration": true, "groupLabel": "M5dn - General Purpose (Intel, Local SSD)", "instanceClass": "m5dn", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5dn.4xlarge: 16 vCPUs / 64 GiB Memory / 600 GB SSD"}, {"apiName": "m5dn.8xlarge", "currentGeneration": true, "groupLabel": "M5dn - General Purpose (Intel, Local SSD)", "instanceClass": "m5dn", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5dn.8xlarge: 32 vCPUs / 128 GiB Memory / 1.2 TB SSD"}, {"apiName": "m5dn.12xlarge", "currentGeneration": true, "groupLabel": "M5dn - General Purpose (Intel, Local SSD)", "instanceClass": "m5dn", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5dn.12xlarge: 48 vCPUs / 192 GiB Memory / 1.8 TB SSD"}, {"apiName": "m5dn.16xlarge", "currentGeneration": true, "groupLabel": "M5dn - General Purpose (Intel, Local SSD)", "instanceClass": "m5dn", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5dn.16xlarge: 64 vCPUs / 256 GiB Memory / 2.4 TB SSD"}, {"apiName": "m5dn.24xlarge", "currentGeneration": true, "groupLabel": "M5dn - General Purpose (Intel, Local SSD)", "instanceClass": "m5dn", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5dn.24xlarge: 96 vCPUs / 384 GiB Memory / 3.6 TB SSD"}, {"apiName": "m5dn.metal", "currentGeneration": true, "groupLabel": "M5dn - General Purpose (Intel, Local SSD)", "instanceClass": "m5dn", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5dn.metal: 96 vCPUs / 384 GiB Memory / 3.6 TB SSD"}, {"apiName": "m5n.large", "currentGeneration": true, "groupLabel": "M5n - General <PERSON> (Intel)", "instanceClass": "m5n", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5n.large: 2 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "m5n.xlarge", "currentGeneration": true, "groupLabel": "M5n - General <PERSON> (Intel)", "instanceClass": "m5n", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5n.xlarge: 4 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "m5n.2xlarge", "currentGeneration": true, "groupLabel": "M5n - General <PERSON> (Intel)", "instanceClass": "m5n", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5n.2xlarge: 8 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "m5n.4xlarge", "currentGeneration": true, "groupLabel": "M5n - General <PERSON> (Intel)", "instanceClass": "m5n", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5n.4xlarge: 16 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "m5n.8xlarge", "currentGeneration": true, "groupLabel": "M5n - General <PERSON> (Intel)", "instanceClass": "m5n", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5n.8xlarge: 32 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "m5n.12xlarge", "currentGeneration": true, "groupLabel": "M5n - General <PERSON> (Intel)", "instanceClass": "m5n", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5n.12xlarge: 48 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "m5n.16xlarge", "currentGeneration": true, "groupLabel": "M5n - General <PERSON> (Intel)", "instanceClass": "m5n", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5n.16xlarge: 64 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "m5n.24xlarge", "currentGeneration": true, "groupLabel": "M5n - General <PERSON> (Intel)", "instanceClass": "m5n", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5n.24xlarge: 96 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "m5n.metal", "currentGeneration": true, "groupLabel": "M5n - General <PERSON> (Intel)", "instanceClass": "m5n", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5n.metal: 96 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "m5zn.large", "currentGeneration": true, "groupLabel": "M5zn - General Purpose (Intel, High Speed Cores)", "instanceClass": "m5zn", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5zn.large: 2 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "m5zn.xlarge", "currentGeneration": true, "groupLabel": "M5zn - General Purpose (Intel, High Speed Cores)", "instanceClass": "m5zn", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5zn.xlarge: 4 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "m5zn.2xlarge", "currentGeneration": true, "groupLabel": "M5zn - General Purpose (Intel, High Speed Cores)", "instanceClass": "m5zn", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5zn.2xlarge: 8 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "m5zn.3xlarge", "currentGeneration": true, "groupLabel": "M5zn - General Purpose (Intel, High Speed Cores)", "instanceClass": "m5zn", "memoryBytes": 51539607552, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5zn.3xlarge: 12 vCPUs / 48 GiB Memory / EBS-Only"}, {"apiName": "m5zn.6xlarge", "currentGeneration": true, "groupLabel": "M5zn - General Purpose (Intel, High Speed Cores)", "instanceClass": "m5zn", "memoryBytes": 103079215104, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5zn.6xlarge: 24 vCPUs / 96 GiB Memory / EBS-Only"}, {"apiName": "m5zn.12xlarge", "currentGeneration": true, "groupLabel": "M5zn - General Purpose (Intel, High Speed Cores)", "instanceClass": "m5zn", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5zn.12xlarge: 48 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "m5zn.metal", "currentGeneration": true, "groupLabel": "M5zn - General Purpose (Intel, High Speed Cores)", "instanceClass": "m5zn", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "m5zn.metal: 48 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "m6a.large", "currentGeneration": true, "groupLabel": "M6a - <PERSON> (AMD)", "instanceClass": "m6a", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6a.large: 2 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "m6a.xlarge", "currentGeneration": true, "groupLabel": "M6a - <PERSON> (AMD)", "instanceClass": "m6a", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6a.xlarge: 4 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "m6a.2xlarge", "currentGeneration": true, "groupLabel": "M6a - <PERSON> (AMD)", "instanceClass": "m6a", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6a.2xlarge: 8 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "m6a.4xlarge", "currentGeneration": true, "groupLabel": "M6a - <PERSON> (AMD)", "instanceClass": "m6a", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6a.4xlarge: 16 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "m6a.8xlarge", "currentGeneration": true, "groupLabel": "M6a - <PERSON> (AMD)", "instanceClass": "m6a", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6a.8xlarge: 32 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "m6a.12xlarge", "currentGeneration": true, "groupLabel": "M6a - <PERSON> (AMD)", "instanceClass": "m6a", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6a.12xlarge: 48 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "m6a.16xlarge", "currentGeneration": true, "groupLabel": "M6a - <PERSON> (AMD)", "instanceClass": "m6a", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6a.16xlarge: 64 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "m6a.24xlarge", "currentGeneration": true, "groupLabel": "M6a - <PERSON> (AMD)", "instanceClass": "m6a", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6a.24xlarge: 96 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "m6a.32xlarge", "currentGeneration": true, "groupLabel": "M6a - <PERSON> (AMD)", "instanceClass": "m6a", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6a.32xlarge: 128 vCPUs / 512 GiB Memory / EBS-Only"}, {"apiName": "m6a.48xlarge", "currentGeneration": true, "groupLabel": "M6a - <PERSON> (AMD)", "instanceClass": "m6a", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6a.48xlarge: 192 vCPUs / 768 GiB Memory / EBS-Only"}, {"apiName": "m6a.metal", "currentGeneration": true, "groupLabel": "M6a - <PERSON> (AMD)", "instanceClass": "m6a", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6a.metal: 192 vCPUs / 768 GiB Memory / EBS-Only"}, {"apiName": "m6g.medium", "currentGeneration": true, "groupLabel": "M6g - General P<PERSON> (ARM)", "instanceClass": "m6g", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6g.medium: 1 vCPU / 4 GiB Memory / EBS-Only"}, {"apiName": "m6g.large", "currentGeneration": true, "groupLabel": "M6g - General P<PERSON> (ARM)", "instanceClass": "m6g", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6g.large: 2 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "m6g.xlarge", "currentGeneration": true, "groupLabel": "M6g - General P<PERSON> (ARM)", "instanceClass": "m6g", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6g.xlarge: 4 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "m6g.2xlarge", "currentGeneration": true, "groupLabel": "M6g - General P<PERSON> (ARM)", "instanceClass": "m6g", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6g.2xlarge: 8 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "m6g.4xlarge", "currentGeneration": true, "groupLabel": "M6g - General P<PERSON> (ARM)", "instanceClass": "m6g", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6g.4xlarge: 16 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "m6g.8xlarge", "currentGeneration": true, "groupLabel": "M6g - General P<PERSON> (ARM)", "instanceClass": "m6g", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6g.8xlarge: 32 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "m6g.12xlarge", "currentGeneration": true, "groupLabel": "M6g - General P<PERSON> (ARM)", "instanceClass": "m6g", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6g.12xlarge: 48 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "m6g.16xlarge", "currentGeneration": true, "groupLabel": "M6g - General P<PERSON> (ARM)", "instanceClass": "m6g", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6g.16xlarge: 64 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "m6g.metal", "currentGeneration": true, "groupLabel": "M6g - General P<PERSON> (ARM)", "instanceClass": "m6g", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6g.metal: 64 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "m6gd.medium", "currentGeneration": true, "groupLabel": "M6gd - <PERSON> (ARM, Local SSD)", "instanceClass": "m6gd", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6gd.medium: 1 vCPU / 4 GiB Memory / 59 GB SSD"}, {"apiName": "m6gd.large", "currentGeneration": true, "groupLabel": "M6gd - <PERSON> (ARM, Local SSD)", "instanceClass": "m6gd", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6gd.large: 2 vCPUs / 8 GiB Memory / 118 GB SSD"}, {"apiName": "m6gd.xlarge", "currentGeneration": true, "groupLabel": "M6gd - <PERSON> (ARM, Local SSD)", "instanceClass": "m6gd", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6gd.xlarge: 4 vCPUs / 16 GiB Memory / 237 GB SSD"}, {"apiName": "m6gd.2xlarge", "currentGeneration": true, "groupLabel": "M6gd - <PERSON> (ARM, Local SSD)", "instanceClass": "m6gd", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6gd.2xlarge: 8 vCPUs / 32 GiB Memory / 474 GB SSD"}, {"apiName": "m6gd.4xlarge", "currentGeneration": true, "groupLabel": "M6gd - <PERSON> (ARM, Local SSD)", "instanceClass": "m6gd", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6gd.4xlarge: 16 vCPUs / 64 GiB Memory / 950 GB SSD"}, {"apiName": "m6gd.8xlarge", "currentGeneration": true, "groupLabel": "M6gd - <PERSON> (ARM, Local SSD)", "instanceClass": "m6gd", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6gd.8xlarge: 32 vCPUs / 128 GiB Memory / 1.9 TB SSD"}, {"apiName": "m6gd.12xlarge", "currentGeneration": true, "groupLabel": "M6gd - <PERSON> (ARM, Local SSD)", "instanceClass": "m6gd", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6gd.12xlarge: 48 vCPUs / 192 GiB Memory / 2.85 TB SSD"}, {"apiName": "m6gd.16xlarge", "currentGeneration": true, "groupLabel": "M6gd - <PERSON> (ARM, Local SSD)", "instanceClass": "m6gd", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6gd.16xlarge: 64 vCPUs / 256 GiB Memory / 3.8 TB SSD"}, {"apiName": "m6gd.metal", "currentGeneration": true, "groupLabel": "M6gd - <PERSON> (ARM, Local SSD)", "instanceClass": "m6gd", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6gd.metal: 64 vCPUs / 256 GiB Memory / 3.8 TB SSD"}, {"apiName": "m6i.large", "currentGeneration": true, "groupLabel": "M6i - General <PERSON> (Intel)", "instanceClass": "m6i", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6i.large: 2 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "m6i.xlarge", "currentGeneration": true, "groupLabel": "M6i - General <PERSON> (Intel)", "instanceClass": "m6i", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6i.xlarge: 4 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "m6i.2xlarge", "currentGeneration": true, "groupLabel": "M6i - General <PERSON> (Intel)", "instanceClass": "m6i", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6i.2xlarge: 8 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "m6i.4xlarge", "currentGeneration": true, "groupLabel": "M6i - General <PERSON> (Intel)", "instanceClass": "m6i", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6i.4xlarge: 16 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "m6i.8xlarge", "currentGeneration": true, "groupLabel": "M6i - General <PERSON> (Intel)", "instanceClass": "m6i", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6i.8xlarge: 32 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "m6i.12xlarge", "currentGeneration": true, "groupLabel": "M6i - General <PERSON> (Intel)", "instanceClass": "m6i", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6i.12xlarge: 48 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "m6i.16xlarge", "currentGeneration": true, "groupLabel": "M6i - General <PERSON> (Intel)", "instanceClass": "m6i", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6i.16xlarge: 64 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "m6i.24xlarge", "currentGeneration": true, "groupLabel": "M6i - General <PERSON> (Intel)", "instanceClass": "m6i", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6i.24xlarge: 96 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "m6i.32xlarge", "currentGeneration": true, "groupLabel": "M6i - General <PERSON> (Intel)", "instanceClass": "m6i", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6i.32xlarge: 128 vCPUs / 512 GiB Memory / EBS-Only"}, {"apiName": "m6i.metal", "currentGeneration": true, "groupLabel": "M6i - General <PERSON> (Intel)", "instanceClass": "m6i", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6i.metal: 128 vCPUs / 512 GiB Memory / EBS-Only"}, {"apiName": "mac1.metal", "currentGeneration": true, "groupLabel": "Mac1 - macOS (Intel)", "instanceClass": "mac1", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand"], "label": "mac1.metal: 12 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "p2.xlarge", "currentGeneration": true, "groupLabel": "P2 - GPU (NVIDIA K80)", "instanceClass": "p2", "memoryBytes": 65498251264, "supportedUsageClasses": ["on-demand", "spot"], "label": "p2.xlarge: 4 vCPUs / 61 GiB Memory / EBS-Only"}, {"apiName": "p2.8xlarge", "currentGeneration": true, "groupLabel": "P2 - GPU (NVIDIA K80)", "instanceClass": "p2", "memoryBytes": 523986010112, "supportedUsageClasses": ["on-demand", "spot"], "label": "p2.8xlarge: 32 vCPUs / 488 GiB Memory / EBS-Only"}, {"apiName": "p2.16xlarge", "currentGeneration": true, "groupLabel": "P2 - GPU (NVIDIA K80)", "instanceClass": "p2", "memoryBytes": 785979015168, "supportedUsageClasses": ["on-demand", "spot"], "label": "p2.16xlarge: 64 vCPUs / 732 GiB Memory / EBS-Only"}, {"apiName": "p3.2xlarge", "currentGeneration": true, "groupLabel": "P3 - GPU (NVIDIA V100)", "instanceClass": "p3", "memoryBytes": 65498251264, "supportedUsageClasses": ["on-demand", "spot"], "label": "p3.2xlarge: 8 vCPUs / 61 GiB Memory / EBS-Only"}, {"apiName": "p3.8xlarge", "currentGeneration": true, "groupLabel": "P3 - GPU (NVIDIA V100)", "instanceClass": "p3", "memoryBytes": 261993005056, "supportedUsageClasses": ["on-demand", "spot"], "label": "p3.8xlarge: 32 vCPUs / 244 GiB Memory / EBS-Only"}, {"apiName": "p3.16xlarge", "currentGeneration": true, "groupLabel": "P3 - GPU (NVIDIA V100)", "instanceClass": "p3", "memoryBytes": 523986010112, "supportedUsageClasses": ["on-demand", "spot"], "label": "p3.16xlarge: 64 vCPUs / 488 GiB Memory / EBS-Only"}, {"apiName": "p3dn.24xlarge", "currentGeneration": true, "groupLabel": "P3dn - GPU (NVIDIA V100, Local SSD)", "instanceClass": "p3dn", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "p3dn.24xlarge: 96 vCPUs / 768 GiB Memory / 1.8 TB SSD"}, {"apiName": "p4d.24xlarge", "currentGeneration": true, "groupLabel": "P4 - GPU (Intel, NVIDIA A100)", "instanceClass": "p4d", "memoryBytes": 1236950581248, "supportedUsageClasses": ["capacity-block", "on-demand", "spot"], "label": "p4d.24xlarge: 96 vCPUs / 1,152 GiB Memory / 8 TB SSD"}, {"apiName": "r5.large", "currentGeneration": true, "groupLabel": "R5 - High Memory (Intel)", "instanceClass": "r5", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5.large: 2 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "r5.xlarge", "currentGeneration": true, "groupLabel": "R5 - High Memory (Intel)", "instanceClass": "r5", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5.xlarge: 4 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "r5.2xlarge", "currentGeneration": true, "groupLabel": "R5 - High Memory (Intel)", "instanceClass": "r5", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5.2xlarge: 8 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "r5.4xlarge", "currentGeneration": true, "groupLabel": "R5 - High Memory (Intel)", "instanceClass": "r5", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5.4xlarge: 16 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "r5.8xlarge", "currentGeneration": true, "groupLabel": "R5 - High Memory (Intel)", "instanceClass": "r5", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5.8xlarge: 32 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "r5.12xlarge", "currentGeneration": true, "groupLabel": "R5 - High Memory (Intel)", "instanceClass": "r5", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5.12xlarge: 48 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "r5.16xlarge", "currentGeneration": true, "groupLabel": "R5 - High Memory (Intel)", "instanceClass": "r5", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5.16xlarge: 64 vCPUs / 512 GiB Memory / EBS-Only"}, {"apiName": "r5.24xlarge", "currentGeneration": true, "groupLabel": "R5 - High Memory (Intel)", "instanceClass": "r5", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5.24xlarge: 96 vCPUs / 768 GiB Memory / EBS-Only"}, {"apiName": "r5.metal", "currentGeneration": true, "groupLabel": "R5 - High Memory (Intel)", "instanceClass": "r5", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5.metal: 96 vCPUs / 768 GiB Memory / EBS-Only"}, {"apiName": "r5a.large", "currentGeneration": true, "groupLabel": "R5a - High Memory (AMD)", "instanceClass": "r5a", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5a.large: 2 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "r5a.xlarge", "currentGeneration": true, "groupLabel": "R5a - High Memory (AMD)", "instanceClass": "r5a", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5a.xlarge: 4 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "r5a.2xlarge", "currentGeneration": true, "groupLabel": "R5a - High Memory (AMD)", "instanceClass": "r5a", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5a.2xlarge: 8 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "r5a.4xlarge", "currentGeneration": true, "groupLabel": "R5a - High Memory (AMD)", "instanceClass": "r5a", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5a.4xlarge: 16 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "r5a.8xlarge", "currentGeneration": true, "groupLabel": "R5a - High Memory (AMD)", "instanceClass": "r5a", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5a.8xlarge: 32 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "r5a.12xlarge", "currentGeneration": true, "groupLabel": "R5a - High Memory (AMD)", "instanceClass": "r5a", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5a.12xlarge: 48 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "r5a.16xlarge", "currentGeneration": true, "groupLabel": "R5a - High Memory (AMD)", "instanceClass": "r5a", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5a.16xlarge: 64 vCPUs / 512 GiB Memory / EBS-Only"}, {"apiName": "r5a.24xlarge", "currentGeneration": true, "groupLabel": "R5a - High Memory (AMD)", "instanceClass": "r5a", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5a.24xlarge: 96 vCPUs / 768 GiB Memory / EBS-Only"}, {"apiName": "r5ad.large", "currentGeneration": true, "groupLabel": "R5ad - High Memory (AMD, Local SSD)", "instanceClass": "r5ad", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5ad.large: 2 vCPUs / 16 GiB Memory / 75 GB SSD"}, {"apiName": "r5ad.xlarge", "currentGeneration": true, "groupLabel": "R5ad - High Memory (AMD, Local SSD)", "instanceClass": "r5ad", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5ad.xlarge: 4 vCPUs / 32 GiB Memory / 150 GB SSD"}, {"apiName": "r5ad.2xlarge", "currentGeneration": true, "groupLabel": "R5ad - High Memory (AMD, Local SSD)", "instanceClass": "r5ad", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5ad.2xlarge: 8 vCPUs / 64 GiB Memory / 300 GB SSD"}, {"apiName": "r5ad.4xlarge", "currentGeneration": true, "groupLabel": "R5ad - High Memory (AMD, Local SSD)", "instanceClass": "r5ad", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5ad.4xlarge: 16 vCPUs / 128 GiB Memory / 600 GB SSD"}, {"apiName": "r5ad.8xlarge", "currentGeneration": true, "groupLabel": "R5ad - High Memory (AMD, Local SSD)", "instanceClass": "r5ad", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5ad.8xlarge: 32 vCPUs / 256 GiB Memory / 1.2 TB SSD"}, {"apiName": "r5ad.12xlarge", "currentGeneration": true, "groupLabel": "R5ad - High Memory (AMD, Local SSD)", "instanceClass": "r5ad", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5ad.12xlarge: 48 vCPUs / 384 GiB Memory / 1.8 TB SSD"}, {"apiName": "r5ad.16xlarge", "currentGeneration": true, "groupLabel": "R5ad - High Memory (AMD, Local SSD)", "instanceClass": "r5ad", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5ad.16xlarge: 64 vCPUs / 512 GiB Memory / 2.4 TB SSD"}, {"apiName": "r5ad.24xlarge", "currentGeneration": true, "groupLabel": "R5ad - High Memory (AMD, Local SSD)", "instanceClass": "r5ad", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5ad.24xlarge: 96 vCPUs / 768 GiB Memory / 3.6 TB SSD"}, {"apiName": "r5b.large", "currentGeneration": true, "groupLabel": "R5b - High Memory (Intel)", "instanceClass": "r5b", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5b.large: 2 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "r5b.xlarge", "currentGeneration": true, "groupLabel": "R5b - High Memory (Intel)", "instanceClass": "r5b", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5b.xlarge: 4 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "r5b.2xlarge", "currentGeneration": true, "groupLabel": "R5b - High Memory (Intel)", "instanceClass": "r5b", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5b.2xlarge: 8 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "r5b.4xlarge", "currentGeneration": true, "groupLabel": "R5b - High Memory (Intel)", "instanceClass": "r5b", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5b.4xlarge: 16 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "r5b.8xlarge", "currentGeneration": true, "groupLabel": "R5b - High Memory (Intel)", "instanceClass": "r5b", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5b.8xlarge: 32 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "r5b.12xlarge", "currentGeneration": true, "groupLabel": "R5b - High Memory (Intel)", "instanceClass": "r5b", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5b.12xlarge: 48 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "r5b.16xlarge", "currentGeneration": true, "groupLabel": "R5b - High Memory (Intel)", "instanceClass": "r5b", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5b.16xlarge: 64 vCPUs / 512 GiB Memory / EBS-Only"}, {"apiName": "r5b.24xlarge", "currentGeneration": true, "groupLabel": "R5b - High Memory (Intel)", "instanceClass": "r5b", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5b.24xlarge: 96 vCPUs / 768 GiB Memory / EBS-Only"}, {"apiName": "r5b.metal", "currentGeneration": true, "groupLabel": "R5b - High Memory (Intel)", "instanceClass": "r5b", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5b.metal: 96 vCPUs / 768 GiB Memory / EBS-Only"}, {"apiName": "r5d.large", "currentGeneration": true, "groupLabel": "R5d - High Memory (Intel, Local SSD)", "instanceClass": "r5d", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5d.large: 2 vCPUs / 16 GiB Memory / 75 GB SSD"}, {"apiName": "r5d.xlarge", "currentGeneration": true, "groupLabel": "R5d - High Memory (Intel, Local SSD)", "instanceClass": "r5d", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5d.xlarge: 4 vCPUs / 32 GiB Memory / 150 GB SSD"}, {"apiName": "r5d.2xlarge", "currentGeneration": true, "groupLabel": "R5d - High Memory (Intel, Local SSD)", "instanceClass": "r5d", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5d.2xlarge: 8 vCPUs / 64 GiB Memory / 300 GB SSD"}, {"apiName": "r5d.4xlarge", "currentGeneration": true, "groupLabel": "R5d - High Memory (Intel, Local SSD)", "instanceClass": "r5d", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5d.4xlarge: 16 vCPUs / 128 GiB Memory / 600 GB SSD"}, {"apiName": "r5d.8xlarge", "currentGeneration": true, "groupLabel": "R5d - High Memory (Intel, Local SSD)", "instanceClass": "r5d", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5d.8xlarge: 32 vCPUs / 256 GiB Memory / 1.2 TB SSD"}, {"apiName": "r5d.12xlarge", "currentGeneration": true, "groupLabel": "R5d - High Memory (Intel, Local SSD)", "instanceClass": "r5d", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5d.12xlarge: 48 vCPUs / 384 GiB Memory / 1.8 TB SSD"}, {"apiName": "r5d.16xlarge", "currentGeneration": true, "groupLabel": "R5d - High Memory (Intel, Local SSD)", "instanceClass": "r5d", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5d.16xlarge: 64 vCPUs / 512 GiB Memory / 2.4 TB SSD"}, {"apiName": "r5d.24xlarge", "currentGeneration": true, "groupLabel": "R5d - High Memory (Intel, Local SSD)", "instanceClass": "r5d", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5d.24xlarge: 96 vCPUs / 768 GiB Memory / 3.6 TB SSD"}, {"apiName": "r5d.metal", "currentGeneration": true, "groupLabel": "R5d - High Memory (Intel, Local SSD)", "instanceClass": "r5d", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5d.metal: 96 vCPUs / 768 GiB Memory / 3.6 TB SSD"}, {"apiName": "r5dn.large", "currentGeneration": true, "groupLabel": "R5dn - High Memory (Intel, Local SSD)", "instanceClass": "r5dn", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5dn.large: 2 vCPUs / 16 GiB Memory / 75 GB SSD"}, {"apiName": "r5dn.xlarge", "currentGeneration": true, "groupLabel": "R5dn - High Memory (Intel, Local SSD)", "instanceClass": "r5dn", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5dn.xlarge: 4 vCPUs / 32 GiB Memory / 150 GB SSD"}, {"apiName": "r5dn.2xlarge", "currentGeneration": true, "groupLabel": "R5dn - High Memory (Intel, Local SSD)", "instanceClass": "r5dn", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5dn.2xlarge: 8 vCPUs / 64 GiB Memory / 300 GB SSD"}, {"apiName": "r5dn.4xlarge", "currentGeneration": true, "groupLabel": "R5dn - High Memory (Intel, Local SSD)", "instanceClass": "r5dn", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5dn.4xlarge: 16 vCPUs / 128 GiB Memory / 600 GB SSD"}, {"apiName": "r5dn.8xlarge", "currentGeneration": true, "groupLabel": "R5dn - High Memory (Intel, Local SSD)", "instanceClass": "r5dn", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5dn.8xlarge: 32 vCPUs / 256 GiB Memory / 1.2 TB SSD"}, {"apiName": "r5dn.12xlarge", "currentGeneration": true, "groupLabel": "R5dn - High Memory (Intel, Local SSD)", "instanceClass": "r5dn", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5dn.12xlarge: 48 vCPUs / 384 GiB Memory / 1.8 TB SSD"}, {"apiName": "r5dn.16xlarge", "currentGeneration": true, "groupLabel": "R5dn - High Memory (Intel, Local SSD)", "instanceClass": "r5dn", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5dn.16xlarge: 64 vCPUs / 512 GiB Memory / 2.4 TB SSD"}, {"apiName": "r5dn.24xlarge", "currentGeneration": true, "groupLabel": "R5dn - High Memory (Intel, Local SSD)", "instanceClass": "r5dn", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5dn.24xlarge: 96 vCPUs / 768 GiB Memory / 3.6 TB SSD"}, {"apiName": "r5dn.metal", "currentGeneration": true, "groupLabel": "R5dn - High Memory (Intel, Local SSD)", "instanceClass": "r5dn", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5dn.metal: 96 vCPUs / 768 GiB Memory / 3.6 TB SSD"}, {"apiName": "r5n.large", "currentGeneration": true, "groupLabel": "R5n - High Memory (Intel)", "instanceClass": "r5n", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5n.large: 2 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "r5n.xlarge", "currentGeneration": true, "groupLabel": "R5n - High Memory (Intel)", "instanceClass": "r5n", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5n.xlarge: 4 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "r5n.2xlarge", "currentGeneration": true, "groupLabel": "R5n - High Memory (Intel)", "instanceClass": "r5n", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5n.2xlarge: 8 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "r5n.4xlarge", "currentGeneration": true, "groupLabel": "R5n - High Memory (Intel)", "instanceClass": "r5n", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5n.4xlarge: 16 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "r5n.8xlarge", "currentGeneration": true, "groupLabel": "R5n - High Memory (Intel)", "instanceClass": "r5n", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5n.8xlarge: 32 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "r5n.12xlarge", "currentGeneration": true, "groupLabel": "R5n - High Memory (Intel)", "instanceClass": "r5n", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5n.12xlarge: 48 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "r5n.16xlarge", "currentGeneration": true, "groupLabel": "R5n - High Memory (Intel)", "instanceClass": "r5n", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5n.16xlarge: 64 vCPUs / 512 GiB Memory / EBS-Only"}, {"apiName": "r5n.24xlarge", "currentGeneration": true, "groupLabel": "R5n - High Memory (Intel)", "instanceClass": "r5n", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5n.24xlarge: 96 vCPUs / 768 GiB Memory / EBS-Only"}, {"apiName": "r5n.metal", "currentGeneration": true, "groupLabel": "R5n - High Memory (Intel)", "instanceClass": "r5n", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "r5n.metal: 96 vCPUs / 768 GiB Memory / EBS-Only"}, {"apiName": "r6g.medium", "currentGeneration": true, "groupLabel": "R6g - High Memory (ARM)", "instanceClass": "r6g", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6g.medium: 1 vCPU / 8 GiB Memory / EBS-Only"}, {"apiName": "r6g.large", "currentGeneration": true, "groupLabel": "R6g - High Memory (ARM)", "instanceClass": "r6g", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6g.large: 2 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "r6g.xlarge", "currentGeneration": true, "groupLabel": "R6g - High Memory (ARM)", "instanceClass": "r6g", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6g.xlarge: 4 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "r6g.2xlarge", "currentGeneration": true, "groupLabel": "R6g - High Memory (ARM)", "instanceClass": "r6g", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6g.2xlarge: 8 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "r6g.4xlarge", "currentGeneration": true, "groupLabel": "R6g - High Memory (ARM)", "instanceClass": "r6g", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6g.4xlarge: 16 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "r6g.8xlarge", "currentGeneration": true, "groupLabel": "R6g - High Memory (ARM)", "instanceClass": "r6g", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6g.8xlarge: 32 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "r6g.12xlarge", "currentGeneration": true, "groupLabel": "R6g - High Memory (ARM)", "instanceClass": "r6g", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6g.12xlarge: 48 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "r6g.16xlarge", "currentGeneration": true, "groupLabel": "R6g - High Memory (ARM)", "instanceClass": "r6g", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6g.16xlarge: 64 vCPUs / 512 GiB Memory / EBS-Only"}, {"apiName": "r6g.metal", "currentGeneration": true, "groupLabel": "R6g - High Memory (ARM)", "instanceClass": "r6g", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6g.metal: 64 vCPUs / 512 GiB Memory / EBS-Only"}, {"apiName": "r6gd.medium", "currentGeneration": true, "groupLabel": "R6gd - High Memory (AMD, Local SSD)", "instanceClass": "r6gd", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6gd.medium: 1 vCPU / 8 GiB Memory / 59 GB SSD"}, {"apiName": "r6gd.large", "currentGeneration": true, "groupLabel": "R6gd - High Memory (AMD, Local SSD)", "instanceClass": "r6gd", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6gd.large: 2 vCPUs / 16 GiB Memory / 118 GB SSD"}, {"apiName": "r6gd.xlarge", "currentGeneration": true, "groupLabel": "R6gd - High Memory (AMD, Local SSD)", "instanceClass": "r6gd", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6gd.xlarge: 4 vCPUs / 32 GiB Memory / 237 GB SSD"}, {"apiName": "r6gd.2xlarge", "currentGeneration": true, "groupLabel": "R6gd - High Memory (AMD, Local SSD)", "instanceClass": "r6gd", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6gd.2xlarge: 8 vCPUs / 64 GiB Memory / 474 GB SSD"}, {"apiName": "r6gd.4xlarge", "currentGeneration": true, "groupLabel": "R6gd - High Memory (AMD, Local SSD)", "instanceClass": "r6gd", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6gd.4xlarge: 16 vCPUs / 128 GiB Memory / 950 GB SSD"}, {"apiName": "r6gd.8xlarge", "currentGeneration": true, "groupLabel": "R6gd - High Memory (AMD, Local SSD)", "instanceClass": "r6gd", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6gd.8xlarge: 32 vCPUs / 256 GiB Memory / 1.9 TB SSD"}, {"apiName": "r6gd.12xlarge", "currentGeneration": true, "groupLabel": "R6gd - High Memory (AMD, Local SSD)", "instanceClass": "r6gd", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6gd.12xlarge: 48 vCPUs / 384 GiB Memory / 2.85 TB SSD"}, {"apiName": "r6gd.16xlarge", "currentGeneration": true, "groupLabel": "R6gd - High Memory (AMD, Local SSD)", "instanceClass": "r6gd", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6gd.16xlarge: 64 vCPUs / 512 GiB Memory / 3.8 TB SSD"}, {"apiName": "r6gd.metal", "currentGeneration": true, "groupLabel": "R6gd - High Memory (AMD, Local SSD)", "instanceClass": "r6gd", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6gd.metal: 64 vCPUs / 512 GiB Memory / 3.8 TB SSD"}, {"apiName": "r6i.large", "currentGeneration": true, "groupLabel": "R6i - High Memory (Intel)", "instanceClass": "r6i", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6i.large: 2 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "r6i.xlarge", "currentGeneration": true, "groupLabel": "R6i - High Memory (Intel)", "instanceClass": "r6i", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6i.xlarge: 4 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "r6i.2xlarge", "currentGeneration": true, "groupLabel": "R6i - High Memory (Intel)", "instanceClass": "r6i", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6i.2xlarge: 8 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "r6i.4xlarge", "currentGeneration": true, "groupLabel": "R6i - High Memory (Intel)", "instanceClass": "r6i", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6i.4xlarge: 16 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "r6i.8xlarge", "currentGeneration": true, "groupLabel": "R6i - High Memory (Intel)", "instanceClass": "r6i", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6i.8xlarge: 32 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "r6i.12xlarge", "currentGeneration": true, "groupLabel": "R6i - High Memory (Intel)", "instanceClass": "r6i", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6i.12xlarge: 48 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "r6i.16xlarge", "currentGeneration": true, "groupLabel": "R6i - High Memory (Intel)", "instanceClass": "r6i", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6i.16xlarge: 64 vCPUs / 512 GiB Memory / EBS-Only"}, {"apiName": "r6i.24xlarge", "currentGeneration": true, "groupLabel": "R6i - High Memory (Intel)", "instanceClass": "r6i", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6i.24xlarge: 96 vCPUs / 768 GiB Memory / EBS-Only"}, {"apiName": "r6i.32xlarge", "currentGeneration": true, "groupLabel": "R6i - High Memory (Intel)", "instanceClass": "r6i", "memoryBytes": 1099511627776, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6i.32xlarge: 128 vCPUs / 1,024 GiB Memory / EBS-Only"}, {"apiName": "r6i.metal", "currentGeneration": true, "groupLabel": "R6i - High Memory (Intel)", "instanceClass": "r6i", "memoryBytes": 1099511627776, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6i.metal: 128 vCPUs / 1,024 GiB Memory / EBS-Only"}, {"apiName": "t2.nano", "currentGeneration": true, "groupLabel": "T2 - <PERSON><PERSON><PERSON>", "instanceClass": "t2", "memoryBytes": 536870912, "supportedUsageClasses": ["on-demand"], "label": "t2.nano: 1 vCPU / 0.5 GiB Memory / EBS-Only"}, {"apiName": "t2.micro", "currentGeneration": true, "groupLabel": "T2 - <PERSON><PERSON><PERSON>", "instanceClass": "t2", "memoryBytes": 1073741824, "supportedUsageClasses": ["on-demand", "spot"], "label": "t2.micro: 1 vCPU / 1 GiB Memory / EBS-Only"}, {"apiName": "t2.small", "currentGeneration": true, "groupLabel": "T2 - <PERSON><PERSON><PERSON>", "instanceClass": "t2", "memoryBytes": 2147483648, "supportedUsageClasses": ["on-demand", "spot"], "label": "t2.small: 1 vCPU / 2 GiB Memory / EBS-Only"}, {"apiName": "t2.medium", "currentGeneration": true, "groupLabel": "T2 - <PERSON><PERSON><PERSON>", "instanceClass": "t2", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "t2.medium: 2 vCPUs / 4 GiB Memory / EBS-Only"}, {"apiName": "t2.large", "currentGeneration": true, "groupLabel": "T2 - <PERSON><PERSON><PERSON>", "instanceClass": "t2", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "t2.large: 2 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "t2.xlarge", "currentGeneration": true, "groupLabel": "T2 - <PERSON><PERSON><PERSON>", "instanceClass": "t2", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "t2.xlarge: 4 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "t2.2xlarge", "currentGeneration": true, "groupLabel": "T2 - <PERSON><PERSON><PERSON>", "instanceClass": "t2", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "t2.2xlarge: 8 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "t3.nano", "currentGeneration": true, "groupLabel": "T3 - <PERSON><PERSON><PERSON> (Intel)", "instanceClass": "t3", "memoryBytes": 536870912, "supportedUsageClasses": ["on-demand", "spot"], "label": "t3.nano: 2 vCPUs / 0.5 GiB Memory / EBS-Only"}, {"apiName": "t3.micro", "currentGeneration": true, "groupLabel": "T3 - <PERSON><PERSON><PERSON> (Intel)", "instanceClass": "t3", "memoryBytes": 1073741824, "supportedUsageClasses": ["on-demand", "spot"], "label": "t3.micro: 2 vCPUs / 1 GiB Memory / EBS-Only"}, {"apiName": "t3.small", "currentGeneration": true, "groupLabel": "T3 - <PERSON><PERSON><PERSON> (Intel)", "instanceClass": "t3", "memoryBytes": 2147483648, "supportedUsageClasses": ["on-demand", "spot"], "label": "t3.small: 2 vCPUs / 2 GiB Memory / EBS-Only"}, {"apiName": "t3.medium", "currentGeneration": true, "groupLabel": "T3 - <PERSON><PERSON><PERSON> (Intel)", "instanceClass": "t3", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "t3.medium: 2 vCPUs / 4 GiB Memory / EBS-Only"}, {"apiName": "t3.large", "currentGeneration": true, "groupLabel": "T3 - <PERSON><PERSON><PERSON> (Intel)", "instanceClass": "t3", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "t3.large: 2 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "t3.xlarge", "currentGeneration": true, "groupLabel": "T3 - <PERSON><PERSON><PERSON> (Intel)", "instanceClass": "t3", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "t3.xlarge: 4 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "t3.2xlarge", "currentGeneration": true, "groupLabel": "T3 - <PERSON><PERSON><PERSON> (Intel)", "instanceClass": "t3", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "t3.2xlarge: 8 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "t3a.nano", "currentGeneration": true, "groupLabel": "T3a - Burstable (AMD)", "instanceClass": "t3a", "memoryBytes": 536870912, "supportedUsageClasses": ["on-demand", "spot"], "label": "t3a.nano: 2 vCPUs / 0.5 GiB Memory / EBS-Only"}, {"apiName": "t3a.micro", "currentGeneration": true, "groupLabel": "T3a - Burstable (AMD)", "instanceClass": "t3a", "memoryBytes": 1073741824, "supportedUsageClasses": ["on-demand", "spot"], "label": "t3a.micro: 2 vCPUs / 1 GiB Memory / EBS-Only"}, {"apiName": "t3a.small", "currentGeneration": true, "groupLabel": "T3a - Burstable (AMD)", "instanceClass": "t3a", "memoryBytes": 2147483648, "supportedUsageClasses": ["on-demand", "spot"], "label": "t3a.small: 2 vCPUs / 2 GiB Memory / EBS-Only"}, {"apiName": "t3a.medium", "currentGeneration": true, "groupLabel": "T3a - Burstable (AMD)", "instanceClass": "t3a", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "t3a.medium: 2 vCPUs / 4 GiB Memory / EBS-Only"}, {"apiName": "t3a.large", "currentGeneration": true, "groupLabel": "T3a - Burstable (AMD)", "instanceClass": "t3a", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "t3a.large: 2 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "t3a.xlarge", "currentGeneration": true, "groupLabel": "T3a - Burstable (AMD)", "instanceClass": "t3a", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "t3a.xlarge: 4 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "t3a.2xlarge", "currentGeneration": true, "groupLabel": "T3a - Burstable (AMD)", "instanceClass": "t3a", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "t3a.2xlarge: 8 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "t4g.nano", "currentGeneration": true, "groupLabel": "T4g - Burstable (ARM)", "instanceClass": "t4g", "memoryBytes": 536870912, "supportedUsageClasses": ["on-demand", "spot"], "label": "t4g.nano: 2 vCPUs / 0.5 GiB Memory / EBS-Only"}, {"apiName": "t4g.micro", "currentGeneration": true, "groupLabel": "T4g - Burstable (ARM)", "instanceClass": "t4g", "memoryBytes": 1073741824, "supportedUsageClasses": ["on-demand", "spot"], "label": "t4g.micro: 2 vCPUs / 1 GiB Memory / EBS-Only"}, {"apiName": "t4g.small", "currentGeneration": true, "groupLabel": "T4g - Burstable (ARM)", "instanceClass": "t4g", "memoryBytes": 2147483648, "supportedUsageClasses": ["on-demand", "spot"], "label": "t4g.small: 2 vCPUs / 2 GiB Memory / EBS-Only"}, {"apiName": "t4g.medium", "currentGeneration": true, "groupLabel": "T4g - Burstable (ARM)", "instanceClass": "t4g", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "t4g.medium: 2 vCPUs / 4 GiB Memory / EBS-Only"}, {"apiName": "t4g.large", "currentGeneration": true, "groupLabel": "T4g - Burstable (ARM)", "instanceClass": "t4g", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "t4g.large: 2 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "t4g.xlarge", "currentGeneration": true, "groupLabel": "T4g - Burstable (ARM)", "instanceClass": "t4g", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "t4g.xlarge: 4 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "t4g.2xlarge", "currentGeneration": true, "groupLabel": "T4g - Burstable (ARM)", "instanceClass": "t4g", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "t4g.2xlarge: 8 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "u-12tb1.112xlarge", "currentGeneration": true, "groupLabel": "U - Really High Memory", "instanceClass": "u-12tb1", "memoryBytes": 13194139533312, "supportedUsageClasses": ["on-demand"], "label": "u-12tb1.112xlarge: 448 vCPUs / 12,288 GiB Memory / EBS-Only"}, {"apiName": "u-6tb1.112xlarge", "currentGeneration": true, "groupLabel": "U - Really High Memory", "instanceClass": "u-6tb1", "memoryBytes": 6597069766656, "supportedUsageClasses": ["on-demand"], "label": "u-6tb1.112xlarge: 448 vCPUs / 6,144 GiB Memory / EBS-Only"}, {"apiName": "u-6tb1.56xlarge", "currentGeneration": true, "groupLabel": "U - Really High Memory", "instanceClass": "u-6tb1", "memoryBytes": 6597069766656, "supportedUsageClasses": ["on-demand"], "label": "u-6tb1.56xlarge: 224 vCPUs / 6,144 GiB Memory / EBS-Only"}, {"apiName": "u-9tb1.112xlarge", "currentGeneration": true, "groupLabel": "U - Really High Memory", "instanceClass": "u-9tb1", "memoryBytes": 9895604649984, "supportedUsageClasses": ["on-demand"], "label": "u-9tb1.112xlarge: 448 vCPUs / 9,216 GiB Memory / EBS-Only"}, {"apiName": "c6a.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6a", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6a.large: 2 vCPUs / 4 GiB Memory / EBS-Only"}, {"apiName": "c6a.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6a", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6a.xlarge: 4 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "c6a.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6a", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6a.2xlarge: 8 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "c6a.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6a", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6a.4xlarge: 16 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "c6a.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6a", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6a.8xlarge: 32 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "c6a.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6a", "memoryBytes": 103079215104, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6a.12xlarge: 48 vCPUs / 96 GiB Memory / EBS-Only"}, {"apiName": "c6a.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6a", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6a.16xlarge: 64 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "c6a.24xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6a", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6a.24xlarge: 96 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "c6a.32xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6a", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6a.32xlarge: 128 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "c6a.48xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6a", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6a.48xlarge: 192 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "c6a.metal", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6a", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6a.metal: 192 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "c6id.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6id", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6id.large: 2 vCPUs / 4 GiB Memory / 118 GB SSD"}, {"apiName": "c6id.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6id", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6id.xlarge: 4 vCPUs / 8 GiB Memory / 237 GB SSD"}, {"apiName": "c6id.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6id", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6id.2xlarge: 8 vCPUs / 16 GiB Memory / 474 GB SSD"}, {"apiName": "c6id.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6id", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6id.4xlarge: 16 vCPUs / 32 GiB Memory / 950 GB SSD"}, {"apiName": "c6id.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6id", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6id.8xlarge: 32 vCPUs / 64 GiB Memory / 1.9 TB SSD"}, {"apiName": "c6id.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6id", "memoryBytes": 103079215104, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6id.12xlarge: 48 vCPUs / 96 GiB Memory / 2.85 TB SSD"}, {"apiName": "c6id.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6id", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6id.16xlarge: 64 vCPUs / 128 GiB Memory / 3.8 TB SSD"}, {"apiName": "c6id.24xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6id", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6id.24xlarge: 96 vCPUs / 192 GiB Memory / 5.7 TB SSD"}, {"apiName": "c6id.32xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6id", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6id.32xlarge: 128 vCPUs / 256 GiB Memory / 7.6 TB SSD"}, {"apiName": "c6id.metal", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6id", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6id.metal: 128 vCPUs / 256 GiB Memory / 7.6 TB SSD"}, {"apiName": "c6in.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6in", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6in.large: 2 vCPUs / 4 GiB Memory / EBS-Only"}, {"apiName": "c6in.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6in", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6in.xlarge: 4 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "c6in.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6in", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6in.2xlarge: 8 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "c6in.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6in", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6in.4xlarge: 16 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "c6in.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6in", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6in.8xlarge: 32 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "c6in.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6in", "memoryBytes": 103079215104, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6in.12xlarge: 48 vCPUs / 96 GiB Memory / EBS-Only"}, {"apiName": "c6in.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6in", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6in.16xlarge: 64 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "c6in.24xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6in", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6in.24xlarge: 96 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "c6in.32xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6in", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6in.32xlarge: 128 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "c6in.metal", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c6in", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "c6in.metal: 128 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "c7a.medium", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7a", "memoryBytes": 2147483648, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7a.medium: 1 vCPU / 2 GiB Memory / EBS-Only"}, {"apiName": "c7a.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7a", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7a.large: 2 vCPUs / 4 GiB Memory / EBS-Only"}, {"apiName": "c7a.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7a", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7a.xlarge: 4 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "c7a.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7a", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7a.2xlarge: 8 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "c7a.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7a", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7a.4xlarge: 16 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "c7a.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7a", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7a.8xlarge: 32 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "c7a.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7a", "memoryBytes": 103079215104, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7a.12xlarge: 48 vCPUs / 96 GiB Memory / EBS-Only"}, {"apiName": "c7a.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7a", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7a.16xlarge: 64 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "c7a.24xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7a", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7a.24xlarge: 96 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "c7a.32xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7a", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7a.32xlarge: 128 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "c7a.48xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7a", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7a.48xlarge: 192 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "c7a.metal-48xl", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7a", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7a.metal-48xl: 192 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "c7g.medium", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7g", "memoryBytes": 2147483648, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7g.medium: 1 vCPU / 2 GiB Memory / EBS-Only"}, {"apiName": "c7g.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7g", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7g.large: 2 vCPUs / 4 GiB Memory / EBS-Only"}, {"apiName": "c7g.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7g", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7g.xlarge: 4 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "c7g.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7g", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7g.2xlarge: 8 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "c7g.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7g", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7g.4xlarge: 16 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "c7g.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7g", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7g.8xlarge: 32 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "c7g.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7g", "memoryBytes": 103079215104, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7g.12xlarge: 48 vCPUs / 96 GiB Memory / EBS-Only"}, {"apiName": "c7g.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7g", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7g.16xlarge: 64 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "c7g.metal", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7g", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7g.metal: 64 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "c7gd.medium", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7gd", "memoryBytes": 2147483648, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7gd.medium: 1 vCPU / 2 GiB Memory / 59 GB SSD"}, {"apiName": "c7gd.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7gd", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7gd.large: 2 vCPUs / 4 GiB Memory / 118 GB SSD"}, {"apiName": "c7gd.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7gd", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7gd.xlarge: 4 vCPUs / 8 GiB Memory / 237 GB SSD"}, {"apiName": "c7gd.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7gd", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7gd.2xlarge: 8 vCPUs / 16 GiB Memory / 474 GB SSD"}, {"apiName": "c7gd.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7gd", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7gd.4xlarge: 16 vCPUs / 32 GiB Memory / 950 GB SSD"}, {"apiName": "c7gd.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7gd", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7gd.8xlarge: 32 vCPUs / 64 GiB Memory / 1.9 TB SSD"}, {"apiName": "c7gd.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7gd", "memoryBytes": 103079215104, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7gd.12xlarge: 48 vCPUs / 96 GiB Memory / 2.85 TB SSD"}, {"apiName": "c7gd.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7gd", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7gd.16xlarge: 64 vCPUs / 128 GiB Memory / 3.8 TB SSD"}, {"apiName": "c7gd.metal", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7gd", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7gd.metal: 64 vCPUs / 128 GiB Memory / 3.8 TB SSD"}, {"apiName": "c7gn.medium", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7gn", "memoryBytes": 2147483648, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7gn.medium: 1 vCPU / 2 GiB Memory / EBS-Only"}, {"apiName": "c7gn.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7gn", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7gn.large: 2 vCPUs / 4 GiB Memory / EBS-Only"}, {"apiName": "c7gn.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7gn", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7gn.xlarge: 4 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "c7gn.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7gn", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7gn.2xlarge: 8 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "c7gn.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7gn", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7gn.4xlarge: 16 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "c7gn.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7gn", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7gn.8xlarge: 32 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "c7gn.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7gn", "memoryBytes": 103079215104, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7gn.12xlarge: 48 vCPUs / 96 GiB Memory / EBS-Only"}, {"apiName": "c7gn.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7gn", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7gn.16xlarge: 64 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "c7i.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7i", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7i.large: 2 vCPUs / 4 GiB Memory / EBS-Only"}, {"apiName": "c7i.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7i", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7i.xlarge: 4 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "c7i.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7i", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7i.2xlarge: 8 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "c7i.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7i", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7i.4xlarge: 16 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "c7i.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7i", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7i.8xlarge: 32 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "c7i.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7i", "memoryBytes": 103079215104, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7i.12xlarge: 48 vCPUs / 96 GiB Memory / EBS-Only"}, {"apiName": "c7i.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7i", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7i.16xlarge: 64 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "c7i.24xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7i", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7i.24xlarge: 96 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "c7i.metal-24xl", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7i", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7i.metal-24xl: 96 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "c7i.48xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7i", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7i.48xlarge: 192 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "c7i.metal-48xl", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "c7i", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "c7i.metal-48xl: 192 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "dl2q.24xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "dl2q", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "dl2q.24xlarge: 96 vCPUs / 768 GiB Memory / EBS-Only"}, {"apiName": "i4g.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "i4g", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "i4g.large: 2 vCPUs / 16 GiB Memory / 468 GB SSD"}, {"apiName": "i4g.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "i4g", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "i4g.xlarge: 4 vCPUs / 32 GiB Memory / 937 GB SSD"}, {"apiName": "i4g.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "i4g", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "i4g.2xlarge: 8 vCPUs / 64 GiB Memory / 1.875 TB SSD"}, {"apiName": "i4g.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "i4g", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "i4g.4xlarge: 16 vCPUs / 128 GiB Memory / 3.75 TB SSD"}, {"apiName": "i4g.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "i4g", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "i4g.8xlarge: 32 vCPUs / 256 GiB Memory / 7.5 TB SSD"}, {"apiName": "i4g.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "i4g", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "i4g.16xlarge: 64 vCPUs / 512 GiB Memory / 15 TB SSD"}, {"apiName": "inf2.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "inf2", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "inf2.xlarge: 4 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "inf2.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "inf2", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "inf2.8xlarge: 32 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "inf2.24xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "inf2", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "inf2.24xlarge: 96 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "inf2.48xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "inf2", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "inf2.48xlarge: 192 vCPUs / 768 GiB Memory / EBS-Only"}, {"apiName": "is4gen.medium", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "is4gen", "memoryBytes": 6442450944, "supportedUsageClasses": ["on-demand", "spot"], "label": "is4gen.medium: 1 vCPU / 6 GiB Memory / 937 GB SSD"}, {"apiName": "is4gen.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "is4gen", "memoryBytes": 12884901888, "supportedUsageClasses": ["on-demand", "spot"], "label": "is4gen.large: 2 vCPUs / 12 GiB Memory / 1.875 TB SSD"}, {"apiName": "is4gen.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "is4gen", "memoryBytes": 25769803776, "supportedUsageClasses": ["on-demand", "spot"], "label": "is4gen.xlarge: 4 vCPUs / 24 GiB Memory / 3.75 TB SSD"}, {"apiName": "is4gen.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "is4gen", "memoryBytes": 51539607552, "supportedUsageClasses": ["on-demand", "spot"], "label": "is4gen.2xlarge: 8 vCPUs / 48 GiB Memory / 7.5 TB SSD"}, {"apiName": "is4gen.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "is4gen", "memoryBytes": 103079215104, "supportedUsageClasses": ["on-demand", "spot"], "label": "is4gen.4xlarge: 16 vCPUs / 96 GiB Memory / 15 TB SSD"}, {"apiName": "is4gen.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "is4gen", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "is4gen.8xlarge: 32 vCPUs / 192 GiB Memory / 30 TB SSD"}, {"apiName": "m6id.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6id", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6id.large: 2 vCPUs / 8 GiB Memory / 118 GB SSD"}, {"apiName": "m6id.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6id", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6id.xlarge: 4 vCPUs / 16 GiB Memory / 237 GB SSD"}, {"apiName": "m6id.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6id", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6id.2xlarge: 8 vCPUs / 32 GiB Memory / 474 GB SSD"}, {"apiName": "m6id.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6id", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6id.4xlarge: 16 vCPUs / 64 GiB Memory / 950 GB SSD"}, {"apiName": "m6id.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6id", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6id.8xlarge: 32 vCPUs / 128 GiB Memory / 1.9 TB SSD"}, {"apiName": "m6id.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6id", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6id.12xlarge: 48 vCPUs / 192 GiB Memory / 2.85 TB SSD"}, {"apiName": "m6id.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6id", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6id.16xlarge: 64 vCPUs / 256 GiB Memory / 3.8 TB SSD"}, {"apiName": "m6id.24xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6id", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6id.24xlarge: 96 vCPUs / 384 GiB Memory / 5.7 TB SSD"}, {"apiName": "m6id.32xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6id", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6id.32xlarge: 128 vCPUs / 512 GiB Memory / 7.6 TB SSD"}, {"apiName": "m6id.metal", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6id", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6id.metal: 128 vCPUs / 512 GiB Memory / 7.6 TB SSD"}, {"apiName": "m6idn.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6idn", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6idn.large: 2 vCPUs / 8 GiB Memory / 118 GB SSD"}, {"apiName": "m6idn.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6idn", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6idn.xlarge: 4 vCPUs / 16 GiB Memory / 237 GB SSD"}, {"apiName": "m6idn.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6idn", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6idn.2xlarge: 8 vCPUs / 32 GiB Memory / 474 GB SSD"}, {"apiName": "m6idn.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6idn", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6idn.4xlarge: 16 vCPUs / 64 GiB Memory / 950 GB SSD"}, {"apiName": "m6idn.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6idn", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6idn.8xlarge: 32 vCPUs / 128 GiB Memory / 1.9 TB SSD"}, {"apiName": "m6idn.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6idn", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6idn.12xlarge: 48 vCPUs / 192 GiB Memory / 2.85 TB SSD"}, {"apiName": "m6idn.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6idn", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6idn.16xlarge: 64 vCPUs / 256 GiB Memory / 3.8 TB SSD"}, {"apiName": "m6idn.24xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6idn", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6idn.24xlarge: 96 vCPUs / 384 GiB Memory / 5.7 TB SSD"}, {"apiName": "m6idn.32xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6idn", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6idn.32xlarge: 128 vCPUs / 512 GiB Memory / 7.6 TB SSD"}, {"apiName": "m6idn.metal", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6idn", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6idn.metal: 128 vCPUs / 512 GiB Memory / 7.6 TB SSD"}, {"apiName": "m6in.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6in", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6in.large: 2 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "m6in.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6in", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6in.xlarge: 4 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "m6in.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6in", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6in.2xlarge: 8 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "m6in.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6in", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6in.4xlarge: 16 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "m6in.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6in", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6in.8xlarge: 32 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "m6in.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6in", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6in.12xlarge: 48 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "m6in.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6in", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6in.16xlarge: 64 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "m6in.24xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6in", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6in.24xlarge: 96 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "m6in.32xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6in", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6in.32xlarge: 128 vCPUs / 512 GiB Memory / EBS-Only"}, {"apiName": "m6in.metal", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m6in", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "m6in.metal: 128 vCPUs / 512 GiB Memory / EBS-Only"}, {"apiName": "m7a.medium", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7a", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7a.medium: 1 vCPU / 4 GiB Memory / EBS-Only"}, {"apiName": "m7a.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7a", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7a.large: 2 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "m7a.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7a", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7a.xlarge: 4 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "m7a.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7a", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7a.2xlarge: 8 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "m7a.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7a", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7a.4xlarge: 16 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "m7a.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7a", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7a.8xlarge: 32 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "m7a.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7a", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7a.12xlarge: 48 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "m7a.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7a", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7a.16xlarge: 64 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "m7a.24xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7a", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7a.24xlarge: 96 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "m7a.32xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7a", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7a.32xlarge: 128 vCPUs / 512 GiB Memory / EBS-Only"}, {"apiName": "m7a.48xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7a", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7a.48xlarge: 192 vCPUs / 768 GiB Memory / EBS-Only"}, {"apiName": "m7a.metal-48xl", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7a", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7a.metal-48xl: 192 vCPUs / 768 GiB Memory / EBS-Only"}, {"apiName": "m7g.medium", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7g", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7g.medium: 1 vCPU / 4 GiB Memory / EBS-Only"}, {"apiName": "m7g.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7g", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7g.large: 2 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "m7g.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7g", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7g.xlarge: 4 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "m7g.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7g", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7g.2xlarge: 8 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "m7g.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7g", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7g.4xlarge: 16 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "m7g.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7g", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7g.8xlarge: 32 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "m7g.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7g", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7g.12xlarge: 48 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "m7g.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7g", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7g.16xlarge: 64 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "m7g.metal", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7g", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7g.metal: 64 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "m7gd.medium", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7gd", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7gd.medium: 1 vCPU / 4 GiB Memory / 59 GB SSD"}, {"apiName": "m7gd.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7gd", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7gd.large: 2 vCPUs / 8 GiB Memory / 118 GB SSD"}, {"apiName": "m7gd.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7gd", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7gd.xlarge: 4 vCPUs / 16 GiB Memory / 237 GB SSD"}, {"apiName": "m7gd.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7gd", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7gd.2xlarge: 8 vCPUs / 32 GiB Memory / 474 GB SSD"}, {"apiName": "m7gd.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7gd", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7gd.4xlarge: 16 vCPUs / 64 GiB Memory / 950 GB SSD"}, {"apiName": "m7gd.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7gd", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7gd.8xlarge: 32 vCPUs / 128 GiB Memory / 1.9 TB SSD"}, {"apiName": "m7gd.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7gd", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7gd.12xlarge: 48 vCPUs / 192 GiB Memory / 2.85 TB SSD"}, {"apiName": "m7gd.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7gd", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7gd.16xlarge: 64 vCPUs / 256 GiB Memory / 3.8 TB SSD"}, {"apiName": "m7gd.metal", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7gd", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7gd.metal: 64 vCPUs / 256 GiB Memory / 3.8 TB SSD"}, {"apiName": "m7i.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7i", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7i.large: 2 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "m7i.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7i", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7i.xlarge: 4 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "m7i.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7i", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7i.2xlarge: 8 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "m7i.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7i", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7i.4xlarge: 16 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "m7i.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7i", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7i.8xlarge: 32 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "m7i.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7i", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7i.12xlarge: 48 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "m7i.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7i", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7i.16xlarge: 64 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "m7i.24xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7i", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7i.24xlarge: 96 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "m7i.metal-24xl", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7i", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7i.metal-24xl: 96 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "m7i.48xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7i", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7i.48xlarge: 192 vCPUs / 768 GiB Memory / EBS-Only"}, {"apiName": "m7i.metal-48xl", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7i", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7i.metal-48xl: 192 vCPUs / 768 GiB Memory / EBS-Only"}, {"apiName": "m7i-flex.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7i-flex", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7i-flex.large: 2 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "m7i-flex.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7i-flex", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7i-flex.xlarge: 4 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "m7i-flex.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7i-flex", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7i-flex.2xlarge: 8 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "m7i-flex.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7i-flex", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7i-flex.4xlarge: 16 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "m7i-flex.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "m7i-flex", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "m7i-flex.8xlarge: 32 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "mac2.metal", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "mac2", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand"], "label": "mac2.metal: 8 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "mac2-m2.metal", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "mac2-m2", "memoryBytes": 25769803776, "supportedUsageClasses": ["on-demand"], "label": "mac2-m2.metal: 8 vCPUs / 24 GiB Memory / EBS-Only"}, {"apiName": "mac2-m2pro.metal", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "mac2-m2pro", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand"], "label": "mac2-m2pro.metal: 12 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "p5.48xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "p5", "memoryBytes": 2199023255552, "supportedUsageClasses": ["capacity-block", "on-demand", "spot"], "label": "p5.48xlarge: 192 vCPUs / 2,048 GiB Memory / 30.4 TB SSD"}, {"apiName": "r6a.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6a", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6a.large: 2 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "r6a.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6a", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6a.xlarge: 4 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "r6a.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6a", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6a.2xlarge: 8 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "r6a.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6a", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6a.4xlarge: 16 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "r6a.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6a", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6a.8xlarge: 32 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "r6a.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6a", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6a.12xlarge: 48 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "r6a.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6a", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6a.16xlarge: 64 vCPUs / 512 GiB Memory / EBS-Only"}, {"apiName": "r6a.24xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6a", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6a.24xlarge: 96 vCPUs / 768 GiB Memory / EBS-Only"}, {"apiName": "r6a.32xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6a", "memoryBytes": 1099511627776, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6a.32xlarge: 128 vCPUs / 1,024 GiB Memory / EBS-Only"}, {"apiName": "r6a.48xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6a", "memoryBytes": 1649267441664, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6a.48xlarge: 192 vCPUs / 1,536 GiB Memory / EBS-Only"}, {"apiName": "r6a.metal", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6a", "memoryBytes": 1649267441664, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6a.metal: 192 vCPUs / 1,536 GiB Memory / EBS-Only"}, {"apiName": "r6id.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6id", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6id.large: 2 vCPUs / 16 GiB Memory / 118 GB SSD"}, {"apiName": "r6id.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6id", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6id.xlarge: 4 vCPUs / 32 GiB Memory / 237 GB SSD"}, {"apiName": "r6id.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6id", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6id.2xlarge: 8 vCPUs / 64 GiB Memory / 474 GB SSD"}, {"apiName": "r6id.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6id", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6id.4xlarge: 16 vCPUs / 128 GiB Memory / 950 GB SSD"}, {"apiName": "r6id.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6id", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6id.8xlarge: 32 vCPUs / 256 GiB Memory / 1.9 TB SSD"}, {"apiName": "r6id.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6id", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6id.12xlarge: 48 vCPUs / 384 GiB Memory / 2.85 TB SSD"}, {"apiName": "r6id.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6id", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6id.16xlarge: 64 vCPUs / 512 GiB Memory / 3.8 TB SSD"}, {"apiName": "r6id.24xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6id", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6id.24xlarge: 96 vCPUs / 768 GiB Memory / 5.7 TB SSD"}, {"apiName": "r6id.32xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6id", "memoryBytes": 1099511627776, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6id.32xlarge: 128 vCPUs / 1,024 GiB Memory / 7.6 TB SSD"}, {"apiName": "r6id.metal", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6id", "memoryBytes": 1099511627776, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6id.metal: 128 vCPUs / 1,024 GiB Memory / 7.6 TB SSD"}, {"apiName": "r6idn.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6idn", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6idn.large: 2 vCPUs / 16 GiB Memory / 118 GB SSD"}, {"apiName": "r6idn.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6idn", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6idn.xlarge: 4 vCPUs / 32 GiB Memory / 237 GB SSD"}, {"apiName": "r6idn.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6idn", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6idn.2xlarge: 8 vCPUs / 64 GiB Memory / 474 GB SSD"}, {"apiName": "r6idn.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6idn", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6idn.4xlarge: 16 vCPUs / 128 GiB Memory / 950 GB SSD"}, {"apiName": "r6idn.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6idn", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6idn.8xlarge: 32 vCPUs / 256 GiB Memory / 1.9 TB SSD"}, {"apiName": "r6idn.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6idn", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6idn.12xlarge: 48 vCPUs / 384 GiB Memory / 2.85 TB SSD"}, {"apiName": "r6idn.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6idn", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6idn.16xlarge: 64 vCPUs / 512 GiB Memory / 3.8 TB SSD"}, {"apiName": "r6idn.24xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6idn", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6idn.24xlarge: 96 vCPUs / 768 GiB Memory / 5.7 TB SSD"}, {"apiName": "r6idn.32xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6idn", "memoryBytes": 1099511627776, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6idn.32xlarge: 128 vCPUs / 1,024 GiB Memory / 7.6 TB SSD"}, {"apiName": "r6idn.metal", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6idn", "memoryBytes": 1099511627776, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6idn.metal: 128 vCPUs / 1,024 GiB Memory / 7.6 TB SSD"}, {"apiName": "r6in.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6in", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6in.large: 2 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "r6in.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6in", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6in.xlarge: 4 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "r6in.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6in", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6in.2xlarge: 8 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "r6in.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6in", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6in.4xlarge: 16 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "r6in.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6in", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6in.8xlarge: 32 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "r6in.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6in", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6in.12xlarge: 48 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "r6in.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6in", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6in.16xlarge: 64 vCPUs / 512 GiB Memory / EBS-Only"}, {"apiName": "r6in.24xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6in", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6in.24xlarge: 96 vCPUs / 768 GiB Memory / EBS-Only"}, {"apiName": "r6in.32xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6in", "memoryBytes": 1099511627776, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6in.32xlarge: 128 vCPUs / 1,024 GiB Memory / EBS-Only"}, {"apiName": "r6in.metal", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r6in", "memoryBytes": 1099511627776, "supportedUsageClasses": ["on-demand", "spot"], "label": "r6in.metal: 128 vCPUs / 1,024 GiB Memory / EBS-Only"}, {"apiName": "r7a.medium", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7a", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7a.medium: 1 vCPU / 8 GiB Memory / EBS-Only"}, {"apiName": "r7a.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7a", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7a.large: 2 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "r7a.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7a", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7a.xlarge: 4 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "r7a.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7a", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7a.2xlarge: 8 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "r7a.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7a", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7a.4xlarge: 16 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "r7a.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7a", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7a.8xlarge: 32 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "r7a.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7a", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7a.12xlarge: 48 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "r7a.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7a", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7a.16xlarge: 64 vCPUs / 512 GiB Memory / EBS-Only"}, {"apiName": "r7a.24xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7a", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7a.24xlarge: 96 vCPUs / 768 GiB Memory / EBS-Only"}, {"apiName": "r7a.32xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7a", "memoryBytes": 1099511627776, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7a.32xlarge: 128 vCPUs / 1,024 GiB Memory / EBS-Only"}, {"apiName": "r7a.48xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7a", "memoryBytes": 1649267441664, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7a.48xlarge: 192 vCPUs / 1,536 GiB Memory / EBS-Only"}, {"apiName": "r7a.metal-48xl", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7a", "memoryBytes": 1649267441664, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7a.metal-48xl: 192 vCPUs / 1,536 GiB Memory / EBS-Only"}, {"apiName": "r7g.medium", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7g", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7g.medium: 1 vCPU / 8 GiB Memory / EBS-Only"}, {"apiName": "r7g.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7g", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7g.large: 2 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "r7g.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7g", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7g.xlarge: 4 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "r7g.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7g", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7g.2xlarge: 8 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "r7g.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7g", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7g.4xlarge: 16 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "r7g.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7g", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7g.8xlarge: 32 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "r7g.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7g", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7g.12xlarge: 48 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "r7g.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7g", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7g.16xlarge: 64 vCPUs / 512 GiB Memory / EBS-Only"}, {"apiName": "r7g.metal", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7g", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7g.metal: 64 vCPUs / 512 GiB Memory / EBS-Only"}, {"apiName": "r7gd.medium", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7gd", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7gd.medium: 1 vCPU / 8 GiB Memory / 59 GB SSD"}, {"apiName": "r7gd.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7gd", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7gd.large: 2 vCPUs / 16 GiB Memory / 118 GB SSD"}, {"apiName": "r7gd.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7gd", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7gd.xlarge: 4 vCPUs / 32 GiB Memory / 237 GB SSD"}, {"apiName": "r7gd.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7gd", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7gd.2xlarge: 8 vCPUs / 64 GiB Memory / 474 GB SSD"}, {"apiName": "r7gd.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7gd", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7gd.4xlarge: 16 vCPUs / 128 GiB Memory / 950 GB SSD"}, {"apiName": "r7gd.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7gd", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7gd.8xlarge: 32 vCPUs / 256 GiB Memory / 1.9 TB SSD"}, {"apiName": "r7gd.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7gd", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7gd.12xlarge: 48 vCPUs / 384 GiB Memory / 2.85 TB SSD"}, {"apiName": "r7gd.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7gd", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7gd.16xlarge: 64 vCPUs / 512 GiB Memory / 3.8 TB SSD"}, {"apiName": "r7gd.metal", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7gd", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7gd.metal: 64 vCPUs / 512 GiB Memory / 3.8 TB SSD"}, {"apiName": "r7i.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7i", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7i.large: 2 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "r7i.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7i", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7i.xlarge: 4 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "r7i.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7i", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7i.2xlarge: 8 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "r7i.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7i", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7i.4xlarge: 16 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "r7i.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7i", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7i.8xlarge: 32 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "r7i.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7i", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7i.12xlarge: 48 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "r7i.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7i", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7i.16xlarge: 64 vCPUs / 512 GiB Memory / EBS-Only"}, {"apiName": "r7i.24xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7i", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7i.24xlarge: 96 vCPUs / 768 GiB Memory / EBS-Only"}, {"apiName": "r7i.metal-24xl", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7i", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7i.metal-24xl: 96 vCPUs / 768 GiB Memory / EBS-Only"}, {"apiName": "r7i.48xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7i", "memoryBytes": 1649267441664, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7i.48xlarge: 192 vCPUs / 1,536 GiB Memory / EBS-Only"}, {"apiName": "r7i.metal-48xl", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7i", "memoryBytes": 1649267441664, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7i.metal-48xl: 192 vCPUs / 1,536 GiB Memory / EBS-Only"}, {"apiName": "r7iz.large", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7iz", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7iz.large: 2 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "r7iz.xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7iz", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7iz.xlarge: 4 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "r7iz.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7iz", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7iz.2xlarge: 8 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "r7iz.4xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7iz", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7iz.4xlarge: 16 vCPUs / 128 GiB Memory / EBS-Only"}, {"apiName": "r7iz.8xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7iz", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7iz.8xlarge: 32 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "r7iz.12xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7iz", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7iz.12xlarge: 48 vCPUs / 384 GiB Memory / EBS-Only"}, {"apiName": "r7iz.16xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7iz", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7iz.16xlarge: 64 vCPUs / 512 GiB Memory / EBS-Only"}, {"apiName": "r7iz.metal-16xl", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7iz", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7iz.metal-16xl: 64 vCPUs / 512 GiB Memory / EBS-Only"}, {"apiName": "r7iz.32xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7iz", "memoryBytes": 1099511627776, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7iz.32xlarge: 128 vCPUs / 1,024 GiB Memory / EBS-Only"}, {"apiName": "r7iz.metal-32xl", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "r7iz", "memoryBytes": 1099511627776, "supportedUsageClasses": ["on-demand", "spot"], "label": "r7iz.metal-32xl: 128 vCPUs / 1,024 GiB Memory / EBS-Only"}, {"apiName": "trn1.2xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "trn1", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "trn1.2xlarge: 8 vCPUs / 32 GiB Memory / 474 GB SSD"}, {"apiName": "trn1.32xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "trn1", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "trn1.32xlarge: 128 vCPUs / 512 GiB Memory / 7.6 TB SSD"}, {"apiName": "trn1n.32xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "trn1n", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "trn1n.32xlarge: 128 vCPUs / 512 GiB Memory / 7.6 TB SSD"}, {"apiName": "u-18tb1.112xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "u-18tb1", "memoryBytes": 19791209299968, "supportedUsageClasses": ["on-demand"], "label": "u-18tb1.112xlarge: 448 vCPUs / 18,432 GiB Memory / EBS-Only"}, {"apiName": "u-24tb1.112xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "u-24tb1", "memoryBytes": 26388279066624, "supportedUsageClasses": ["on-demand"], "label": "u-24tb1.112xlarge: 448 vCPUs / 24,576 GiB Memory / EBS-Only"}, {"apiName": "u-3tb1.56xlarge", "currentGeneration": true, "groupLabel": "Unknown", "instanceClass": "u-3tb1", "memoryBytes": 3298534883328, "supportedUsageClasses": ["on-demand"], "label": "u-3tb1.56xlarge: 224 vCPUs / 3,072 GiB Memory / EBS-Only"}, {"apiName": "vt1.3xlarge", "currentGeneration": true, "groupLabel": "VT1 - Video Accelerator (Intel, Xilinx U30)", "instanceClass": "vt1", "memoryBytes": 25769803776, "supportedUsageClasses": ["on-demand", "spot"], "label": "vt1.3xlarge: 12 vCPUs / 24 GiB Memory / EBS-Only"}, {"apiName": "vt1.6xlarge", "currentGeneration": true, "groupLabel": "VT1 - Video Accelerator (Intel, Xilinx U30)", "instanceClass": "vt1", "memoryBytes": 51539607552, "supportedUsageClasses": ["on-demand", "spot"], "label": "vt1.6xlarge: 24 vCPUs / 48 GiB Memory / EBS-Only"}, {"apiName": "vt1.24xlarge", "currentGeneration": true, "groupLabel": "VT1 - Video Accelerator (Intel, Xilinx U30)", "instanceClass": "vt1", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "vt1.24xlarge: 96 vCPUs / 192 GiB Memory / EBS-Only"}, {"apiName": "x1.16xlarge", "currentGeneration": true, "groupLabel": "X1 - Really High Memory", "instanceClass": "x1", "memoryBytes": 1047972020224, "supportedUsageClasses": ["on-demand", "spot"], "label": "x1.16xlarge: 64 vCPUs / 976 GiB Memory / 1.92 TB SSD"}, {"apiName": "x1.32xlarge", "currentGeneration": true, "groupLabel": "X1 - Really High Memory", "instanceClass": "x1", "memoryBytes": 2095944040448, "supportedUsageClasses": ["on-demand", "spot"], "label": "x1.32xlarge: 128 vCPUs / 1,952 GiB Memory / 3.84 TB SSD"}, {"apiName": "x1e.xlarge", "currentGeneration": true, "groupLabel": "X1e - Really High Memory", "instanceClass": "x1e", "memoryBytes": 130996502528, "supportedUsageClasses": ["on-demand", "spot"], "label": "x1e.xlarge: 4 vCPUs / 122 GiB Memory / 120 GB SSD"}, {"apiName": "x1e.2xlarge", "currentGeneration": true, "groupLabel": "X1e - Really High Memory", "instanceClass": "x1e", "memoryBytes": 261993005056, "supportedUsageClasses": ["on-demand", "spot"], "label": "x1e.2xlarge: 8 vCPUs / 244 GiB Memory / 240 GB SSD"}, {"apiName": "x1e.4xlarge", "currentGeneration": true, "groupLabel": "X1e - Really High Memory", "instanceClass": "x1e", "memoryBytes": 523986010112, "supportedUsageClasses": ["on-demand", "spot"], "label": "x1e.4xlarge: 16 vCPUs / 488 GiB Memory / 480 GB SSD"}, {"apiName": "x1e.8xlarge", "currentGeneration": true, "groupLabel": "X1e - Really High Memory", "instanceClass": "x1e", "memoryBytes": 1047972020224, "supportedUsageClasses": ["on-demand", "spot"], "label": "x1e.8xlarge: 32 vCPUs / 976 GiB Memory / 960 GB SSD"}, {"apiName": "x1e.16xlarge", "currentGeneration": true, "groupLabel": "X1e - Really High Memory", "instanceClass": "x1e", "memoryBytes": 2095944040448, "supportedUsageClasses": ["on-demand", "spot"], "label": "x1e.16xlarge: 64 vCPUs / 1,952 GiB Memory / 1.92 TB SSD"}, {"apiName": "x1e.32xlarge", "currentGeneration": true, "groupLabel": "X1e - Really High Memory", "instanceClass": "x1e", "memoryBytes": 4191888080896, "supportedUsageClasses": ["on-demand", "spot"], "label": "x1e.32xlarge: 128 vCPUs / 3,904 GiB Memory / 3.84 TB SSD"}, {"apiName": "x2gd.medium", "currentGeneration": true, "groupLabel": "X2gd - High Memory (ARM, Local NVMe)", "instanceClass": "x2gd", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2gd.medium: 1 vCPU / 16 GiB Memory / 59 GB SSD"}, {"apiName": "x2gd.large", "currentGeneration": true, "groupLabel": "X2gd - High Memory (ARM, Local NVMe)", "instanceClass": "x2gd", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2gd.large: 2 vCPUs / 32 GiB Memory / 118 GB SSD"}, {"apiName": "x2gd.xlarge", "currentGeneration": true, "groupLabel": "X2gd - High Memory (ARM, Local NVMe)", "instanceClass": "x2gd", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2gd.xlarge: 4 vCPUs / 64 GiB Memory / 237 GB SSD"}, {"apiName": "x2gd.2xlarge", "currentGeneration": true, "groupLabel": "X2gd - High Memory (ARM, Local NVMe)", "instanceClass": "x2gd", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2gd.2xlarge: 8 vCPUs / 128 GiB Memory / 475 GB SSD"}, {"apiName": "x2gd.4xlarge", "currentGeneration": true, "groupLabel": "X2gd - High Memory (ARM, Local NVMe)", "instanceClass": "x2gd", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2gd.4xlarge: 16 vCPUs / 256 GiB Memory / 950 GB SSD"}, {"apiName": "x2gd.8xlarge", "currentGeneration": true, "groupLabel": "X2gd - High Memory (ARM, Local NVMe)", "instanceClass": "x2gd", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2gd.8xlarge: 32 vCPUs / 512 GiB Memory / 1.9 TB SSD"}, {"apiName": "x2gd.12xlarge", "currentGeneration": true, "groupLabel": "X2gd - High Memory (ARM, Local NVMe)", "instanceClass": "x2gd", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2gd.12xlarge: 48 vCPUs / 768 GiB Memory / 2.85 TB SSD"}, {"apiName": "x2gd.16xlarge", "currentGeneration": true, "groupLabel": "X2gd - High Memory (ARM, Local NVMe)", "instanceClass": "x2gd", "memoryBytes": 1099511627776, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2gd.16xlarge: 64 vCPUs / 1,024 GiB Memory / 3.8 TB SSD"}, {"apiName": "x2gd.metal", "currentGeneration": true, "groupLabel": "X2gd - High Memory (ARM, Local NVMe)", "instanceClass": "x2gd", "memoryBytes": 1099511627776, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2gd.metal: 64 vCPUs / 1,024 GiB Memory / 3.8 TB SSD"}, {"apiName": "x2idn.16xlarge", "currentGeneration": true, "groupLabel": "X2idn - High Memory (Intel, Local NVMe)", "instanceClass": "x2idn", "memoryBytes": 1099511627776, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2idn.16xlarge: 64 vCPUs / 1,024 GiB Memory / 1.9 TB SSD"}, {"apiName": "x2idn.24xlarge", "currentGeneration": true, "groupLabel": "X2idn - High Memory (Intel, Local NVMe)", "instanceClass": "x2idn", "memoryBytes": 1649267441664, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2idn.24xlarge: 96 vCPUs / 1,536 GiB Memory / 2.85 TB SSD"}, {"apiName": "x2idn.32xlarge", "currentGeneration": true, "groupLabel": "X2idn - High Memory (Intel, Local NVMe)", "instanceClass": "x2idn", "memoryBytes": 2199023255552, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2idn.32xlarge: 128 vCPUs / 2,048 GiB Memory / 3.8 TB SSD"}, {"apiName": "x2idn.metal", "currentGeneration": true, "groupLabel": "X2idn - High Memory (Intel, Local NVMe)", "instanceClass": "x2idn", "memoryBytes": 2199023255552, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2idn.metal: 128 vCPUs / 2,048 GiB Memory / 3.8 TB SSD"}, {"apiName": "x2iedn.xlarge", "currentGeneration": true, "groupLabel": "X2iedn - High Memory (Intel, Local NVMe)", "instanceClass": "x2iedn", "memoryBytes": 137438953472, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2iedn.xlarge: 4 vCPUs / 128 GiB Memory / 118 GB SSD"}, {"apiName": "x2iedn.2xlarge", "currentGeneration": true, "groupLabel": "X2iedn - High Memory (Intel, Local NVMe)", "instanceClass": "x2iedn", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2iedn.2xlarge: 8 vCPUs / 256 GiB Memory / 237 GB SSD"}, {"apiName": "x2iedn.4xlarge", "currentGeneration": true, "groupLabel": "X2iedn - High Memory (Intel, Local NVMe)", "instanceClass": "x2iedn", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2iedn.4xlarge: 16 vCPUs / 512 GiB Memory / 475 GB SSD"}, {"apiName": "x2iedn.8xlarge", "currentGeneration": true, "groupLabel": "X2iedn - High Memory (Intel, Local NVMe)", "instanceClass": "x2iedn", "memoryBytes": 1099511627776, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2iedn.8xlarge: 32 vCPUs / 1,024 GiB Memory / 950 GB SSD"}, {"apiName": "x2iedn.16xlarge", "currentGeneration": true, "groupLabel": "X2iedn - High Memory (Intel, Local NVMe)", "instanceClass": "x2iedn", "memoryBytes": 2199023255552, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2iedn.16xlarge: 64 vCPUs / 2,048 GiB Memory / 1.9 TB SSD"}, {"apiName": "x2iedn.24xlarge", "currentGeneration": true, "groupLabel": "X2iedn - High Memory (Intel, Local NVMe)", "instanceClass": "x2iedn", "memoryBytes": 3298534883328, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2iedn.24xlarge: 96 vCPUs / 3,072 GiB Memory / 2.85 TB SSD"}, {"apiName": "x2iedn.32xlarge", "currentGeneration": true, "groupLabel": "X2iedn - High Memory (Intel, Local NVMe)", "instanceClass": "x2iedn", "memoryBytes": 4398046511104, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2iedn.32xlarge: 128 vCPUs / 4,096 GiB Memory / 3.8 TB SSD"}, {"apiName": "x2iedn.metal", "currentGeneration": true, "groupLabel": "X2iedn - High Memory (Intel, Local NVMe)", "instanceClass": "x2iedn", "memoryBytes": 4398046511104, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2iedn.metal: 128 vCPUs / 4,096 GiB Memory / 3.8 TB SSD"}, {"apiName": "x2iezn.2xlarge", "currentGeneration": true, "groupLabel": "X2iezn - High Memory (Intel)", "instanceClass": "x2iezn", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2iezn.2xlarge: 8 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "x2iezn.4xlarge", "currentGeneration": true, "groupLabel": "X2iezn - High Memory (Intel)", "instanceClass": "x2iezn", "memoryBytes": 549755813888, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2iezn.4xlarge: 16 vCPUs / 512 GiB Memory / EBS-Only"}, {"apiName": "x2iezn.6xlarge", "currentGeneration": true, "groupLabel": "X2iezn - High Memory (Intel)", "instanceClass": "x2iezn", "memoryBytes": 824633720832, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2iezn.6xlarge: 24 vCPUs / 768 GiB Memory / EBS-Only"}, {"apiName": "x2iezn.8xlarge", "currentGeneration": true, "groupLabel": "X2iezn - High Memory (Intel)", "instanceClass": "x2iezn", "memoryBytes": 1099511627776, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2iezn.8xlarge: 32 vCPUs / 1,024 GiB Memory / EBS-Only"}, {"apiName": "x2iezn.12xlarge", "currentGeneration": true, "groupLabel": "X2iezn - High Memory (Intel)", "instanceClass": "x2iezn", "memoryBytes": 1649267441664, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2iezn.12xlarge: 48 vCPUs / 1,536 GiB Memory / EBS-Only"}, {"apiName": "x2iezn.metal", "currentGeneration": true, "groupLabel": "X2iezn - High Memory (Intel)", "instanceClass": "x2iezn", "memoryBytes": 1649267441664, "supportedUsageClasses": ["on-demand", "spot"], "label": "x2iezn.metal: 48 vCPUs / 1,536 GiB Memory / EBS-Only"}, {"apiName": "z1d.large", "currentGeneration": true, "groupLabel": "Z1d - High Memory (Local SSD)", "instanceClass": "z1d", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "z1d.large: 2 vCPUs / 16 GiB Memory / 75 GB SSD"}, {"apiName": "z1d.xlarge", "currentGeneration": true, "groupLabel": "Z1d - High Memory (Local SSD)", "instanceClass": "z1d", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "z1d.xlarge: 4 vCPUs / 32 GiB Memory / 150 GB SSD"}, {"apiName": "z1d.2xlarge", "currentGeneration": true, "groupLabel": "Z1d - High Memory (Local SSD)", "instanceClass": "z1d", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "z1d.2xlarge: 8 vCPUs / 64 GiB Memory / 300 GB SSD"}, {"apiName": "z1d.3xlarge", "currentGeneration": true, "groupLabel": "Z1d - High Memory (Local SSD)", "instanceClass": "z1d", "memoryBytes": 103079215104, "supportedUsageClasses": ["on-demand", "spot"], "label": "z1d.3xlarge: 12 vCPUs / 96 GiB Memory / 450 GB SSD"}, {"apiName": "z1d.6xlarge", "currentGeneration": true, "groupLabel": "Z1d - High Memory (Local SSD)", "instanceClass": "z1d", "memoryBytes": 206158430208, "supportedUsageClasses": ["on-demand", "spot"], "label": "z1d.6xlarge: 24 vCPUs / 192 GiB Memory / 900 GB SSD"}, {"apiName": "z1d.12xlarge", "currentGeneration": true, "groupLabel": "Z1d - High Memory (Local SSD)", "instanceClass": "z1d", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "z1d.12xlarge: 48 vCPUs / 384 GiB Memory / 1.8 TB SSD"}, {"apiName": "z1d.metal", "currentGeneration": true, "groupLabel": "Z1d - High Memory (Local SSD)", "instanceClass": "z1d", "memoryBytes": 412316860416, "supportedUsageClasses": ["on-demand", "spot"], "label": "z1d.metal: 48 vCPUs / 384 GiB Memory / 1.8 TB SSD"}, {"apiName": "a1.medium", "currentGeneration": false, "groupLabel": "A1 - ARM", "instanceClass": "a1", "memoryBytes": 2147483648, "supportedUsageClasses": ["on-demand", "spot"], "label": "a1.medium: 1 vCPU / 2 GiB Memory / EBS-Only"}, {"apiName": "a1.large", "currentGeneration": false, "groupLabel": "A1 - ARM", "instanceClass": "a1", "memoryBytes": 4294967296, "supportedUsageClasses": ["on-demand", "spot"], "label": "a1.large: 2 vCPUs / 4 GiB Memory / EBS-Only"}, {"apiName": "a1.xlarge", "currentGeneration": false, "groupLabel": "A1 - ARM", "instanceClass": "a1", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "a1.xlarge: 4 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "a1.2xlarge", "currentGeneration": false, "groupLabel": "A1 - ARM", "instanceClass": "a1", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "a1.2xlarge: 8 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "a1.4xlarge", "currentGeneration": false, "groupLabel": "A1 - ARM", "instanceClass": "a1", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "a1.4xlarge: 16 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "a1.metal", "currentGeneration": false, "groupLabel": "A1 - ARM", "instanceClass": "a1", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "a1.metal: 16 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "c1.medium", "currentGeneration": false, "groupLabel": "C1 - High-CPU (Intel)", "instanceClass": "c1", "memoryBytes": 1824522240, "supportedUsageClasses": ["on-demand", "spot"], "label": "c1.medium: 2 vCPUs / 1.699 GiB Memory / 350 GB HDD"}, {"apiName": "c1.xlarge", "currentGeneration": false, "groupLabel": "C1 - High-CPU (Intel)", "instanceClass": "c1", "memoryBytes": 7516192768, "supportedUsageClasses": ["on-demand", "spot"], "label": "c1.xlarge: 8 vCPUs / 7 GiB Memory / 1.68 TB HDD"}, {"apiName": "c3.large", "currentGeneration": false, "groupLabel": "C3 - High CPU (Intel)", "instanceClass": "c3", "memoryBytes": 4026531840, "supportedUsageClasses": ["on-demand", "spot"], "label": "c3.large: 2 vCPUs / 3.75 GiB Memory / 32 GB SSD"}, {"apiName": "c3.xlarge", "currentGeneration": false, "groupLabel": "C3 - High CPU (Intel)", "instanceClass": "c3", "memoryBytes": 8053063680, "supportedUsageClasses": ["on-demand", "spot"], "label": "c3.xlarge: 4 vCPUs / 7.5 GiB Memory / 80 GB SSD"}, {"apiName": "c3.2xlarge", "currentGeneration": false, "groupLabel": "C3 - High CPU (Intel)", "instanceClass": "c3", "memoryBytes": 16106127360, "supportedUsageClasses": ["on-demand", "spot"], "label": "c3.2xlarge: 8 vCPUs / 15 GiB Memory / 160 GB SSD"}, {"apiName": "c3.4xlarge", "currentGeneration": false, "groupLabel": "C3 - High CPU (Intel)", "instanceClass": "c3", "memoryBytes": 32212254720, "supportedUsageClasses": ["on-demand", "spot"], "label": "c3.4xlarge: 16 vCPUs / 30 GiB Memory / 320 GB SSD"}, {"apiName": "c3.8xlarge", "currentGeneration": false, "groupLabel": "C3 - High CPU (Intel)", "instanceClass": "c3", "memoryBytes": 64424509440, "supportedUsageClasses": ["on-demand", "spot"], "label": "c3.8xlarge: 32 vCPUs / 60 GiB Memory / 640 GB SSD"}, {"apiName": "c4.large", "currentGeneration": false, "groupLabel": "C4 - High CPU (Intel)", "instanceClass": "c4", "memoryBytes": 4026531840, "supportedUsageClasses": ["on-demand", "spot"], "label": "c4.large: 2 vCPUs / 3.75 GiB Memory / EBS-Only"}, {"apiName": "c4.xlarge", "currentGeneration": false, "groupLabel": "C4 - High CPU (Intel)", "instanceClass": "c4", "memoryBytes": 8053063680, "supportedUsageClasses": ["on-demand", "spot"], "label": "c4.xlarge: 4 vCPUs / 7.5 GiB Memory / EBS-Only"}, {"apiName": "c4.2xlarge", "currentGeneration": false, "groupLabel": "C4 - High CPU (Intel)", "instanceClass": "c4", "memoryBytes": 16106127360, "supportedUsageClasses": ["on-demand", "spot"], "label": "c4.2xlarge: 8 vCPUs / 15 GiB Memory / EBS-Only"}, {"apiName": "c4.4xlarge", "currentGeneration": false, "groupLabel": "C4 - High CPU (Intel)", "instanceClass": "c4", "memoryBytes": 32212254720, "supportedUsageClasses": ["on-demand", "spot"], "label": "c4.4xlarge: 16 vCPUs / 30 GiB Memory / EBS-Only"}, {"apiName": "c4.8xlarge", "currentGeneration": false, "groupLabel": "C4 - High CPU (Intel)", "instanceClass": "c4", "memoryBytes": 64424509440, "supportedUsageClasses": ["on-demand", "spot"], "label": "c4.8xlarge: 36 vCPUs / 60 GiB Memory / EBS-Only"}, {"apiName": "d2.xlarge", "currentGeneration": false, "groupLabel": "D2 - High Density Storage (Local HDD)", "instanceClass": "d2", "memoryBytes": 32749125632, "supportedUsageClasses": ["on-demand", "spot"], "label": "d2.xlarge: 4 vCPUs / 30.5 GiB Memory / 6.144 TB HDD"}, {"apiName": "d2.2xlarge", "currentGeneration": false, "groupLabel": "D2 - High Density Storage (Local HDD)", "instanceClass": "d2", "memoryBytes": 65498251264, "supportedUsageClasses": ["on-demand", "spot"], "label": "d2.2xlarge: 8 vCPUs / 61 GiB Memory / 12.288 TB HDD"}, {"apiName": "d2.4xlarge", "currentGeneration": false, "groupLabel": "D2 - High Density Storage (Local HDD)", "instanceClass": "d2", "memoryBytes": 130996502528, "supportedUsageClasses": ["on-demand", "spot"], "label": "d2.4xlarge: 16 vCPUs / 122 GiB Memory / 24.576 TB HDD"}, {"apiName": "d2.8xlarge", "currentGeneration": false, "groupLabel": "D2 - High Density Storage (Local HDD)", "instanceClass": "d2", "memoryBytes": 261993005056, "supportedUsageClasses": ["on-demand", "spot"], "label": "d2.8xlarge: 36 vCPUs / 244 GiB Memory / 49.152 TB HDD"}, {"apiName": "i2.xlarge", "currentGeneration": false, "groupLabel": "I2 - High I/O Storage (Local SSD)", "instanceClass": "i2", "memoryBytes": 32749125632, "supportedUsageClasses": ["on-demand", "spot"], "label": "i2.xlarge: 4 vCPUs / 30.5 GiB Memory / 800 GB SSD"}, {"apiName": "i2.2xlarge", "currentGeneration": false, "groupLabel": "I2 - High I/O Storage (Local SSD)", "instanceClass": "i2", "memoryBytes": 65498251264, "supportedUsageClasses": ["on-demand", "spot"], "label": "i2.2xlarge: 8 vCPUs / 61 GiB Memory / 1.6 TB SSD"}, {"apiName": "i2.4xlarge", "currentGeneration": false, "groupLabel": "I2 - High I/O Storage (Local SSD)", "instanceClass": "i2", "memoryBytes": 130996502528, "supportedUsageClasses": ["on-demand", "spot"], "label": "i2.4xlarge: 16 vCPUs / 122 GiB Memory / 3.2 TB SSD"}, {"apiName": "i2.8xlarge", "currentGeneration": false, "groupLabel": "I2 - High I/O Storage (Local SSD)", "instanceClass": "i2", "memoryBytes": 261993005056, "supportedUsageClasses": ["on-demand", "spot"], "label": "i2.8xlarge: 32 vCPUs / 244 GiB Memory / 6.4 TB SSD"}, {"apiName": "m1.small", "currentGeneration": false, "groupLabel": "M1 - General <PERSON>", "instanceClass": "m1", "memoryBytes": 1824522240, "supportedUsageClasses": ["on-demand", "spot"], "label": "m1.small: 1 vCPU / 1.699 GiB Memory / 160 GB HDD"}, {"apiName": "m1.medium", "currentGeneration": false, "groupLabel": "M1 - General <PERSON>", "instanceClass": "m1", "memoryBytes": 3972005888, "supportedUsageClasses": ["on-demand", "spot"], "label": "m1.medium: 1 vCPU / 3.699 GiB Memory / 410 GB HDD"}, {"apiName": "m1.large", "currentGeneration": false, "groupLabel": "M1 - General <PERSON>", "instanceClass": "m1", "memoryBytes": 8053063680, "supportedUsageClasses": ["on-demand", "spot"], "label": "m1.large: 2 vCPUs / 7.5 GiB Memory / 840 GB HDD"}, {"apiName": "m1.xlarge", "currentGeneration": false, "groupLabel": "M1 - General <PERSON>", "instanceClass": "m1", "memoryBytes": 16106127360, "supportedUsageClasses": ["on-demand", "spot"], "label": "m1.xlarge: 4 vCPUs / 15 GiB Memory / 1.68 TB HDD"}, {"apiName": "m2.xlarge", "currentGeneration": false, "groupLabel": "M2 - General <PERSON>", "instanceClass": "m2", "memoryBytes": 18360565760, "supportedUsageClasses": ["on-demand", "spot"], "label": "m2.xlarge: 2 vCPUs / 17.1 GiB Memory / 420 GB HDD"}, {"apiName": "m2.2xlarge", "currentGeneration": false, "groupLabel": "M2 - General <PERSON>", "instanceClass": "m2", "memoryBytes": 36721131520, "supportedUsageClasses": ["on-demand", "spot"], "label": "m2.2xlarge: 4 vCPUs / 34.199 GiB Memory / 850 GB HDD"}, {"apiName": "m2.4xlarge", "currentGeneration": false, "groupLabel": "M2 - General <PERSON>", "instanceClass": "m2", "memoryBytes": 73443311616, "supportedUsageClasses": ["on-demand", "spot"], "label": "m2.4xlarge: 8 vCPUs / 68.399 GiB Memory / 1.68 TB HDD"}, {"apiName": "m3.medium", "currentGeneration": false, "groupLabel": "M3 - General <PERSON>", "instanceClass": "m3", "memoryBytes": 4026531840, "supportedUsageClasses": ["on-demand", "spot"], "label": "m3.medium: 1 vCPU / 3.75 GiB Memory / 4 GB SSD"}, {"apiName": "m3.large", "currentGeneration": false, "groupLabel": "M3 - General <PERSON>", "instanceClass": "m3", "memoryBytes": 8053063680, "supportedUsageClasses": ["on-demand", "spot"], "label": "m3.large: 2 vCPUs / 7.5 GiB Memory / 32 GB SSD"}, {"apiName": "m3.xlarge", "currentGeneration": false, "groupLabel": "M3 - General <PERSON>", "instanceClass": "m3", "memoryBytes": 16106127360, "supportedUsageClasses": ["on-demand", "spot"], "label": "m3.xlarge: 4 vCPUs / 15 GiB Memory / 80 GB SSD"}, {"apiName": "m3.2xlarge", "currentGeneration": false, "groupLabel": "M3 - General <PERSON>", "instanceClass": "m3", "memoryBytes": 32212254720, "supportedUsageClasses": ["on-demand", "spot"], "label": "m3.2xlarge: 8 vCPUs / 30 GiB Memory / 160 GB SSD"}, {"apiName": "m4.large", "currentGeneration": false, "groupLabel": "M4 - General <PERSON>", "instanceClass": "m4", "memoryBytes": 8589934592, "supportedUsageClasses": ["on-demand", "spot"], "label": "m4.large: 2 vCPUs / 8 GiB Memory / EBS-Only"}, {"apiName": "m4.xlarge", "currentGeneration": false, "groupLabel": "M4 - General <PERSON>", "instanceClass": "m4", "memoryBytes": 17179869184, "supportedUsageClasses": ["on-demand", "spot"], "label": "m4.xlarge: 4 vCPUs / 16 GiB Memory / EBS-Only"}, {"apiName": "m4.2xlarge", "currentGeneration": false, "groupLabel": "M4 - General <PERSON>", "instanceClass": "m4", "memoryBytes": 34359738368, "supportedUsageClasses": ["on-demand", "spot"], "label": "m4.2xlarge: 8 vCPUs / 32 GiB Memory / EBS-Only"}, {"apiName": "m4.4xlarge", "currentGeneration": false, "groupLabel": "M4 - General <PERSON>", "instanceClass": "m4", "memoryBytes": 68719476736, "supportedUsageClasses": ["on-demand", "spot"], "label": "m4.4xlarge: 16 vCPUs / 64 GiB Memory / EBS-Only"}, {"apiName": "m4.10xlarge", "currentGeneration": false, "groupLabel": "M4 - General <PERSON>", "instanceClass": "m4", "memoryBytes": 171798691840, "supportedUsageClasses": ["on-demand", "spot"], "label": "m4.10xlarge: 40 vCPUs / 160 GiB Memory / EBS-Only"}, {"apiName": "m4.16xlarge", "currentGeneration": false, "groupLabel": "M4 - General <PERSON>", "instanceClass": "m4", "memoryBytes": 274877906944, "supportedUsageClasses": ["on-demand", "spot"], "label": "m4.16xlarge: 64 vCPUs / 256 GiB Memory / EBS-Only"}, {"apiName": "r3.large", "currentGeneration": false, "groupLabel": "R3 - High Memory", "instanceClass": "r3", "memoryBytes": 16106127360, "supportedUsageClasses": ["on-demand", "spot"], "label": "r3.large: 2 vCPUs / 15 GiB Memory / 32 GB SSD"}, {"apiName": "r3.xlarge", "currentGeneration": false, "groupLabel": "R3 - High Memory", "instanceClass": "r3", "memoryBytes": 32749125632, "supportedUsageClasses": ["on-demand", "spot"], "label": "r3.xlarge: 4 vCPUs / 30.5 GiB Memory / 80 GB SSD"}, {"apiName": "r3.2xlarge", "currentGeneration": false, "groupLabel": "R3 - High Memory", "instanceClass": "r3", "memoryBytes": 65498251264, "supportedUsageClasses": ["on-demand", "spot"], "label": "r3.2xlarge: 8 vCPUs / 61 GiB Memory / 160 GB SSD"}, {"apiName": "r3.4xlarge", "currentGeneration": false, "groupLabel": "R3 - High Memory", "instanceClass": "r3", "memoryBytes": 130996502528, "supportedUsageClasses": ["on-demand", "spot"], "label": "r3.4xlarge: 16 vCPUs / 122 GiB Memory / 320 GB SSD"}, {"apiName": "r3.8xlarge", "currentGeneration": false, "groupLabel": "R3 - High Memory", "instanceClass": "r3", "memoryBytes": 261993005056, "supportedUsageClasses": ["on-demand", "spot"], "label": "r3.8xlarge: 32 vCPUs / 244 GiB Memory / 640 GB SSD"}, {"apiName": "r4.large", "currentGeneration": false, "groupLabel": "R4 - High Memory", "instanceClass": "r4", "memoryBytes": 16374562816, "supportedUsageClasses": ["on-demand", "spot"], "label": "r4.large: 2 vCPUs / 15.25 GiB Memory / EBS-Only"}, {"apiName": "r4.xlarge", "currentGeneration": false, "groupLabel": "R4 - High Memory", "instanceClass": "r4", "memoryBytes": 32749125632, "supportedUsageClasses": ["on-demand", "spot"], "label": "r4.xlarge: 4 vCPUs / 30.5 GiB Memory / EBS-Only"}, {"apiName": "r4.2xlarge", "currentGeneration": false, "groupLabel": "R4 - High Memory", "instanceClass": "r4", "memoryBytes": 65498251264, "supportedUsageClasses": ["on-demand", "spot"], "label": "r4.2xlarge: 8 vCPUs / 61 GiB Memory / EBS-Only"}, {"apiName": "r4.4xlarge", "currentGeneration": false, "groupLabel": "R4 - High Memory", "instanceClass": "r4", "memoryBytes": 130996502528, "supportedUsageClasses": ["on-demand", "spot"], "label": "r4.4xlarge: 16 vCPUs / 122 GiB Memory / EBS-Only"}, {"apiName": "r4.8xlarge", "currentGeneration": false, "groupLabel": "R4 - High Memory", "instanceClass": "r4", "memoryBytes": 261993005056, "supportedUsageClasses": ["on-demand", "spot"], "label": "r4.8xlarge: 32 vCPUs / 244 GiB Memory / EBS-Only"}, {"apiName": "r4.16xlarge", "currentGeneration": false, "groupLabel": "R4 - High Memory", "instanceClass": "r4", "memoryBytes": 523986010112, "supportedUsageClasses": ["on-demand", "spot"], "label": "r4.16xlarge: 64 vCPUs / 488 GiB Memory / EBS-Only"}, {"apiName": "t1.micro", "currentGeneration": false, "groupLabel": "T1 - <PERSON><PERSON><PERSON>", "instanceClass": "t1", "memoryBytes": 657457152, "supportedUsageClasses": ["on-demand", "spot"], "label": "t1.micro: 1 vCPU / 0.612 GiB Memory / EBS-Only"}]