
/* eslint-disable jest/no-mocks-import */
import { _CREATE, _EDIT } from '@shell/config/query-params';
import { shallowMount } from '@vue/test-utils';
import Networking from '../Networking.vue';

import SecurityGroupData from '../__mocks__/describeSecurityGroups.js';
import SubnetData from '../__mocks__/describeSubnets';
import VpcData from '../__mocks__/describeVpcs';
import InternetGatewayData from '../__mocks__/describeInternetGateways.js';
import RouteTableData from '../__mocks__/describeRouteTables.js';

// const mockedValidationMixin = {
//   computed: {
//     fvFormIsValid:                jest.fn(),
//     type:                         jest.fn(),
//     fvUnreportedValidationErrors: jest.fn(),
//   },
//   methods: { fvGetAndReportPathRules: jest.fn() }
// };

const mockedStore = () => {
  return {
    getters: {
      'i18n/t': (text: string) => text,
      t:        (text: string) => text,
    },
  };
};

const mockedRoute = { query: {} };

const requiredSetup = () => {
  return {
    // mixins: [mockedValidationMixin],
    global: {
      mocks: {
        $store:      mockedStore(),
        $route:      mockedRoute,
        $fetchState: {},
      }
    }
  };
};

describe('eKS Networking', () => {
  it('should allow the user to add endpoints when public access is checked', async() => {
    const setup = requiredSetup();

    const wrapper = shallowMount(Networking, {
      propsData: {},
      ...setup
    });

    const publicAccessSources = wrapper.getComponent('[data-testid="eks-public-access-sources"]');

    expect(publicAccessSources.vm.addAllowed).toBe(false);

    wrapper.setProps({ publicAccess: true });
    await wrapper.vm.$nextTick();
    expect(publicAccessSources.vm.addAllowed).toBe(true);
  });

  it('should show a vpc and subnet selector when the select from existing subnets radio option is selected', async() => {
    const setup = requiredSetup();

    const wrapper = shallowMount(Networking, {
      propsData: { mode: _CREATE },
      ...setup
    });

    wrapper.setData({ chooseSubnet: true });
    await wrapper.vm.$nextTick();
    const subnetDropdown = wrapper.findComponent('[data-testid="eks-subnets-dropdown"]');

    expect(subnetDropdown.exists()).toBe(true);

    wrapper.setData({ chooseSubnet: false });
    await wrapper.vm.$nextTick();
    expect(subnetDropdown.exists()).toBe(false);
  });

  it('should show a list of subnets in use if a cluster has already provisioned and the \'create automatically\' vpc option was selected', async() => {
    const setup = requiredSetup();

    const wrapper = shallowMount(Networking, {
      propsData: {
        mode: _EDIT, subnets: [], statusSubnets: ['bc', 'def']
      },
      ...setup
    });

    await wrapper.vm.$nextTick();

    const subnetDropdown = wrapper.findComponent('[data-testid="eks-subnets-dropdown"]');

    expect(subnetDropdown.exists()).toBe(true);

    expect(subnetDropdown.props().value).toStrictEqual(['bc', 'def']);
  });

  // this tests dropdown visibility - contents of dropdown are tested further down
  it('should show a dropdown of security groups when the select from existing subnets radio option is selected', async() => {
    const setup = requiredSetup();

    const wrapper = shallowMount(Networking, {
      propsData: { mode: _CREATE },
      ...setup
    });

    wrapper.setData({ chooseSubnet: true });
    await wrapper.vm.$nextTick();
    const sgDropDown = wrapper.findComponent('[data-testid="eks-security-groups-dropdown"]');

    expect(sgDropDown.exists()).toBe(true);

    wrapper.setData({ chooseSubnet: false });
    await wrapper.vm.$nextTick();
    expect(sgDropDown.exists()).toBe(false);
  });

  it('should fetch VPCs and securityGroups when a credential is provided or changes', async() => {
    const setup = requiredSetup();

    // eslint-disable-next-line prefer-const
    let wrapper: any;

    const fetchGroupsSpy = jest.spyOn(Networking.methods, 'fetchSecurityGroups').mockImplementation(() => {
      wrapper.setData({ securityGroupInfo: SecurityGroupData });
    });

    const fetchVpcsSpy = jest.spyOn(Networking.methods, 'fetchVpcs').mockImplementation(() => {
      wrapper.setData({ subnetInfo: SubnetData.Subnets, vpcInfo: VpcData.Vpcs });
    });

    wrapper = shallowMount(Networking, {
      propsData: { mode: _CREATE },
      ...setup
    });

    wrapper.setProps({ amazonCredentialSecret: 'abc' });
    await wrapper.vm.$nextTick();

    expect(fetchGroupsSpy).toHaveBeenCalledTimes(1);
    expect(fetchVpcsSpy).toHaveBeenCalledTimes(1);

    wrapper.setProps({ amazonCredentialSecret: 'def' });
    await wrapper.vm.$nextTick();

    expect(fetchGroupsSpy).toHaveBeenCalledTimes(2);
    expect(fetchVpcsSpy).toHaveBeenCalledTimes(2);
  });

  it('should populate the security group dropdown with groups in the same vpc as the selected subnet', async() => {
    const setup = requiredSetup();

    // eslint-disable-next-line prefer-const
    let wrapper: any;

    jest.spyOn(Networking.methods, 'fetchSecurityGroups').mockImplementation(() => {
      wrapper.setData({ securityGroupInfo: SecurityGroupData });
    });

    jest.spyOn(Networking.methods, 'fetchVpcs').mockImplementation(() => {
      wrapper.setData({ subnetInfo: SubnetData.Subnets, vpcInfo: VpcData.Vpcs });
    });

    wrapper = shallowMount(Networking, {
      propsData: { mode: _CREATE },
      ...setup
    });

    wrapper.setProps({ amazonCredentialSecret: 'abc' });
    wrapper.setData({ chooseSubnet: true });

    await wrapper.vm.$nextTick();

    const sgDropDown = wrapper.getComponent('[data-testid="eks-security-groups-dropdown"]');

    expect(sgDropDown.props().options).toStrictEqual([]);

    wrapper.setProps({ subnets: ['subnet-1234'] });
    await wrapper.vm.$nextTick();
    expect(sgDropDown.props().options).toStrictEqual([{ label: 'group0-name (group0-id)', value: 'group0-id' }, { label: 'group1-name (group1-id)', value: 'group1-id' }]);

    wrapper.setProps({ subnets: ['subnet-4321'] });
    await wrapper.vm.$nextTick();
    expect(sgDropDown.props().options).toStrictEqual([{ label: 'group2-name (group2-id)', value: 'group2-id' }]);
  });

  it('should clear any selected security groups when the selected vpc changes', async() => {
    const setup = requiredSetup();

    // eslint-disable-next-line prefer-const
    let wrapper: any;

    jest.spyOn(Networking.methods, 'fetchSecurityGroups').mockImplementation(() => {
      wrapper.setData({ securityGroupInfo: SecurityGroupData });
    });

    jest.spyOn(Networking.methods, 'fetchVpcs').mockImplementation(() => {
      wrapper.setData({ subnetInfo: SubnetData.Subnets, vpcInfo: VpcData.Vpcs });
    });

    wrapper = shallowMount(Networking, {
      propsData: {
        mode: _CREATE, subnets: ['subnet-1234'], securityGroups: ['group0-id']
      },
      ...setup
    });

    wrapper.setProps({ amazonCredentialSecret: 'abc' });
    wrapper.setData({ chooseSubnet: true });

    await wrapper.vm.$nextTick();
    expect(wrapper.emitted('update:securityGroups')).toBeUndefined();

    wrapper.setProps({ subnets: ['subnet-12345'] });
    await wrapper.vm.$nextTick();

    expect(wrapper.emitted('update:securityGroups')[0][0]).toStrictEqual([]);
  });

  it('should not allow the user to select subnets in different vpcs', async() => {
    const setup = requiredSetup();

    // eslint-disable-next-line prefer-const
    let wrapper: any;

    jest.spyOn(Networking.methods, 'fetchSecurityGroups').mockImplementation(() => {
      wrapper.setData({ securityGroupInfo: SecurityGroupData });
    });

    jest.spyOn(Networking.methods, 'fetchVpcs').mockImplementation(() => {
      wrapper.setData({ subnetInfo: SubnetData.Subnets, vpcInfo: VpcData.Vpcs });
    });

    wrapper = shallowMount(Networking, {
      propsData: { mode: _CREATE, subnets: ['subnet-1234'] },
      ...setup
    });

    wrapper.setProps({ amazonCredentialSecret: 'abc' });
    wrapper.setData({ chooseSubnet: true });

    await wrapper.vm.$nextTick();
    const subnetDropdown = wrapper.getComponent('[data-testid="eks-subnets-dropdown"]');

    let subnetOpts = subnetDropdown.props().options;

    expect(subnetOpts.filter((opt) => !opt.disabled && opt.kind !== 'group')).toHaveLength(2);

    // check that adding a subnet in the same vpc doesnt change the selectable subnet options
    wrapper.setProps({ subnets: ['subnet-4321', 'subnet-1234'] });
    await wrapper.vm.$nextTick();

    subnetOpts = subnetDropdown.props().options;
    expect(subnetOpts.filter((opt) => !opt.disabled && opt.kind !== 'group')).toHaveLength(2);

    // check that when subnets are empty, no options are disabled
    wrapper.setProps({ subnets: [] });
    await wrapper.vm.$nextTick();

    subnetOpts = subnetDropdown.props().options;
    expect(subnetOpts.filter((opt) => opt.disabled && opt.kind !== 'group')).toHaveLength(0);

    // check that selecting a subnet in a different vpc changes which options are disabled
    wrapper.setProps({ subnets: ['subnet-12'] });
    await wrapper.vm.$nextTick();

    subnetOpts = subnetDropdown.props().options;
    expect(subnetOpts.filter((opt) => opt.disabled && opt.kind !== 'group')).toHaveLength(5);
  });

  // Internet Gateway Validation Tests
  describe('Internet Gateway Validation', () => {
    let mockStore: any;
    let wrapper: any;

    beforeEach(() => {
      mockStore = {
        dispatch: jest.fn()
      };

      jest.spyOn(Networking.methods as any, 'fetchSecurityGroups').mockImplementation(function(this: any) {
        if (this && this.setData) {
          this.setData({ securityGroupInfo: SecurityGroupData });
        }
      });

      jest.spyOn(Networking.methods as any, 'fetchVpcs').mockImplementation(function(this: any) {
        if (this && this.setData) {
          this.setData({ subnetInfo: SubnetData.Subnets, vpcInfo: VpcData.Vpcs });
        }
      });
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });

    it('should validate connectivity successfully when internet gateway and routes exist', async() => {
      const setup = requiredSetup();
      
      wrapper = shallowMount(Networking, {
        propsData: {
          mode: _CREATE,
          region: 'us-west-2',
          amazonCredentialSecret: 'test-cred',
          subnets: ['subnet-1234']
        },
        ...setup
      });

      wrapper.vm.$store = mockStore;

      // Mock successful validation response
      const mockResults = {
        hasInternetGateway: true,
        internetGateways: InternetGatewayData.InternetGateways,
        subnetResults: [
          {
            subnetId: 'subnet-1234',
            hasInternetAccess: true,
            routeTableId: 'rtb-custom-1234',
            isUsingMainRouteTable: false
          }
        ]
      };

      mockStore.dispatch.mockResolvedValue(mockResults);

      // Set up test data manually since mocks might not work during initialization
      wrapper.setData({ 
        chooseSubnet: true,
        subnetInfo: SubnetData.Subnets, 
        vpcInfo: VpcData.Vpcs 
      });
      await wrapper.vm.$nextTick();

      await wrapper.vm.validateInternetConnectivity();

      expect(mockStore.dispatch).toHaveBeenCalledWith('aws-saas/validateSubnetConnectivity', {
        region: 'us-west-2',
        cloudCredentialId: 'test-cred',
        vpcId: 'vpc-1234',
        subnetIds: ['subnet-1234']
      });

      expect(wrapper.vm.connectivityResults).toEqual(mockResults);
      expect(wrapper.vm.validationError).toBeNull();
    });

    it('should handle validation failure when internet gateway is missing', async() => {
      const setup = requiredSetup();
      
      wrapper = shallowMount(Networking, {
        propsData: {
          mode: _CREATE,
          region: 'us-west-2',
          amazonCredentialSecret: 'test-cred',
          subnets: ['subnet-4321']
        },
        ...setup
      });

      wrapper.vm.$store = mockStore;

      // Mock validation response with no internet gateway
      const mockResults = {
        hasInternetGateway: false,
        internetGateways: [],
        subnetResults: [
          {
            subnetId: 'subnet-4321',
            hasInternetAccess: false,
            routeTableId: 'rtb-private-4321',
            isUsingMainRouteTable: false
          }
        ]
      };

      mockStore.dispatch.mockResolvedValue(mockResults);

      wrapper.setData({ 
        chooseSubnet: true,
        subnetInfo: SubnetData.Subnets, 
        vpcInfo: VpcData.Vpcs 
      });
      await wrapper.vm.$nextTick();

      await wrapper.vm.validateInternetConnectivity();

      expect(wrapper.vm.connectivityResults).toEqual(mockResults);
      expect(wrapper.vm.connectivityResults.hasInternetGateway).toBe(false);
      expect(wrapper.vm.connectivityResults.subnetResults[0].hasInternetAccess).toBe(false);
    });

    it('should handle validation error with proper error messages', async() => {
      const setup = requiredSetup();
      
      wrapper = shallowMount(Networking, {
        propsData: {
          mode: _CREATE,
          region: 'us-west-2',
          amazonCredentialSecret: 'invalid-cred',
          subnets: ['subnet-1234']
        },
        ...setup
      });

      wrapper.vm.$store = mockStore;

      const mockError = new Error('UnauthorizedOperation: You are not authorized to perform this operation') as any;
      mockError.code = 'UnauthorizedOperation';
      mockStore.dispatch.mockRejectedValue(mockError);

      wrapper.setData({ 
        chooseSubnet: true,
        subnetInfo: SubnetData.Subnets, 
        vpcInfo: VpcData.Vpcs 
      });
      await wrapper.vm.$nextTick();

      await expect(wrapper.vm.validateInternetConnectivity()).rejects.toThrow();

      expect(wrapper.vm.validationError).toBeTruthy();
      expect(wrapper.vm.connectivityResults).toBeNull();
    });

    it('should compute canValidateConnectivity correctly', async() => {
      const setup = requiredSetup();
      
      wrapper = shallowMount(Networking, {
        propsData: {
          mode: _CREATE,
          region: '',
          amazonCredentialSecret: '',
          subnets: []
        },
        ...setup
      });

      wrapper.setData({ chooseSubnet: false });
      expect(wrapper.vm.canValidateConnectivity).toBe(false);

      wrapper.setData({ chooseSubnet: true });
      expect(wrapper.vm.canValidateConnectivity).toBe(false);

      wrapper.setProps({ subnets: ['subnet-1234'] });
      // Need to set subnet info so selectedVpc works
      wrapper.setData({ subnetInfo: SubnetData.Subnets });
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.canValidateConnectivity).toBe(false);

      wrapper.setProps({ region: 'us-west-2' });
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.canValidateConnectivity).toBe(false);

      wrapper.setProps({ amazonCredentialSecret: 'test-cred' });
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.canValidateConnectivity).toBe(true);
    });

    it('should compute validationBannerColor correctly', async() => {
      const setup = requiredSetup();
      
      wrapper = shallowMount(Networking, {
        propsData: { mode: _CREATE },
        ...setup
      });

      // Initialize with null connectivity results
      wrapper.setData({ connectivityResults: null });
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.validationBannerColor).toBe('info');

      // Success case
      wrapper.setData({
        connectivityResults: {
          hasInternetGateway: true,
          internetGateways: [],
          subnetResults: [{ hasInternetAccess: true }]
        }
      });
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.validationBannerColor).toBe('success');

      // Warning case
      wrapper.setData({
        connectivityResults: {
          hasInternetGateway: true,
          internetGateways: [],
          subnetResults: [{ hasInternetAccess: false }]
        }
      });
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.validationBannerColor).toBe('warning');
    });

    it('should compute validationMessage correctly', async() => {
      const setup = requiredSetup();
      
      wrapper = shallowMount(Networking, {
        propsData: { mode: _CREATE },
        ...setup
      });

      wrapper.setData({ 
        subnetInfo: SubnetData.Subnets,
        connectivityResults: null
      });
      await wrapper.vm.$nextTick();

      // No results yet
      expect(wrapper.vm.validationMessage).toBe('');

      // Success case
      wrapper.setData({
        connectivityResults: {
          hasInternetGateway: true,
          internetGateways: [],
          subnetResults: [{ subnetId: 'subnet-1234', hasInternetAccess: true }]
        }
      });
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.validationMessage).toBe('eks.connectivity.validation.success');

      // Warning case with subnet names
      wrapper.setData({
        connectivityResults: {
          hasInternetGateway: true,
          internetGateways: [],
          subnetResults: [{ subnetId: 'subnet-1234', hasInternetAccess: false }]
        }
      });
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.validationMessage).toBe('eks.connectivity.validation.warning');
    });

    it('should handle validateInternetConnectivity with missing parameters', async() => {
      const setup = requiredSetup();
      
      wrapper = shallowMount(Networking, {
        propsData: {
          mode: _CREATE,
          region: '',
          amazonCredentialSecret: '',
          subnets: []
        },
        ...setup
      });

      wrapper.setData({ 
        chooseSubnet: true,
        connectivityResults: null,
        validationError: null
      });
      await wrapper.vm.$nextTick();

      // Should exit early due to canValidateConnectivity check
      await wrapper.vm.validateInternetConnectivity();
      
      expect(wrapper.vm.connectivityResults).toBeNull();
      expect(wrapper.vm.validationError).toBeNull();
    });

    it('should handle handleValidateClick method', async() => {
      const setup = requiredSetup();
      
      wrapper = shallowMount(Networking, {
        propsData: {
          mode: _CREATE,
          region: 'us-west-2',
          amazonCredentialSecret: 'test-cred',
          subnets: ['subnet-1234']
        },
        ...setup
      });

      wrapper.vm.$store = mockStore;
      wrapper.setData({ chooseSubnet: true });

      const mockCallback = jest.fn();
      const mockResults = {
        hasInternetGateway: true,
        internetGateways: InternetGatewayData.InternetGateways,
        subnetResults: [{ subnetId: 'subnet-1234', hasInternetAccess: true }]
      };

      mockStore.dispatch.mockResolvedValue(mockResults);

      await wrapper.vm.handleValidateClick(mockCallback);

      expect(mockCallback).toHaveBeenCalledWith('cancelled');
    });

    it('should display validation UI when conditions are met', async() => {
      const setup = requiredSetup();
      
      wrapper = shallowMount(Networking, {
        propsData: {
          mode: _CREATE,
          region: 'us-west-2',
          amazonCredentialSecret: 'test-cred',
          subnets: ['subnet-1234']
        },
        ...setup
      });

      wrapper.setData({ chooseSubnet: true });
      await wrapper.vm.$nextTick();

      // Check if validation section should be visible (chooseSubnet && isNew)
      expect(wrapper.vm.chooseSubnet).toBe(true);
      expect(wrapper.vm.isNew).toBe(true);
    });

    it('should clear validation results when starting new validation', async() => {
      const setup = requiredSetup();
      
      wrapper = shallowMount(Networking, {
        propsData: {
          mode: _CREATE,
          region: 'us-west-2', 
          amazonCredentialSecret: 'test-cred',
          subnets: ['subnet-1234']
        },
        ...setup
      });

      wrapper.vm.$store = mockStore;
      wrapper.setData({ 
        chooseSubnet: true,
        subnetInfo: SubnetData.Subnets, 
        vpcInfo: VpcData.Vpcs,
        connectivityResults: { hasInternetGateway: false },
        validationError: 'Previous error'
      });

      const mockResults = {
        hasInternetGateway: true,
        internetGateways: InternetGatewayData.InternetGateways,
        subnetResults: [{ subnetId: 'subnet-1234', hasInternetAccess: true }]
      };

      mockStore.dispatch.mockResolvedValue(mockResults);

      await wrapper.vm.validateInternetConnectivity();

      expect(wrapper.vm.connectivityResults).toEqual(mockResults);
      expect(wrapper.vm.validationError).toBeNull();
    });
  });
});
