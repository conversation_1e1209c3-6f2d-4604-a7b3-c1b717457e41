import { importTypes } from '@rancher/auto-import';
import { IPlugin } from '@shell/core/types';
import NotFound from "@shell/pages/404.vue"
import { EKSSaaSProvisioner } from './provisioner';

export default function(plugin: IPlugin): void {
  importTypes(plugin);

  plugin.metadata = require('./package.json');

  plugin.addProduct(require('./product'));

  // Register EKS SaaS provisioner
  plugin.register('provisioner', EKSSaaSProvisioner.ID, EKSSaaSProvisioner);

  // Register AWS SaaS store
  plugin.addStore('aws-saas', () => {
    return (store: any) => {
      const awsSaasStore = require('./store/aws-saas').default;
      store.registerModule('aws-saas', awsSaasStore);
    };
  }, (store: any) => {
    store.unregisterModule('aws-saas');
  });

  // Built-in icon
  plugin.metadata.icon = require('./assets/amazoneks.svg');

  // plugin.addRoute({
  //   name:      'c-cluster-settings',
  //   path:      '/c/:cluster/settings',
  //   component: NotFound
  // });

}
