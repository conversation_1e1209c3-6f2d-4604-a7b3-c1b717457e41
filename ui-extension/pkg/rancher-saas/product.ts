import { fetchSetting } from '@shell/utils/settings';

import posthog from 'posthog-js';

export function configureSettings($plugin:any, store:any) {
  const { product, configureType } = $plugin.DSL(store, 'settings');

  product({ ifHaveType: 'no-such-schema-so-always-hidden' });

  configureType('management.cattle.io.settings', { customRoute: {} });
}

export function configureUiPlugins($plugin:any, store:any) {
  const { product } = $plugin.DSL(store, 'uiplugins');

  product({ ifHaveType: 'no-such-schema-so-always-hidden' });
}

export function configureManager($plugin:any, store:any) {
  const { virtualType, configureType } = $plugin.DSL(store, 'manager');

  configureType('provisioning.cattle.io.cluster', {
    custom: {
      createLocation: (params: any) => {
        return {
          name:  'c-cluster-product-resource-create',
          query: { type: 'amazoneks-saas' },
          params
        };
      },
      importLocation: (params: any) => {
        return {
          name:  'c-cluster-product-resource-create',
          query: {
            mode: 'import',
            type: 'amazoneks-saas'
          },
          params
        };
      },
    }
  });

  virtualType({
    name:       'rke-kontainer-drivers',
    ifHaveType: 'no-such-schema-so-always-hidden',
  });

  virtualType({
    name:       'rke-node-drivers',
    ifHaveType: 'no-such-schema-so-always-hidden',
  });
}

export function configurePosthog($plugin:any, store:any) {
  console.log('configuring posthost');

  fetchSetting(store, 'server-url').then((response: any) => {
    // TODO - maybe bake this - although not sensitive.
    const stagingPosthogKey = 'phc_rF6yIw8VIKgvMVOttK5DfokkcITxK2Wth9iYYoaMzZ3';

    const server = response.value;

    if (server.includes('staging.susecloud.cloud')) {
      console.log('initialisingPosthog');
      posthog.init(stagingPosthogKey,
        {
          api_host:        'https://eu.i.posthog.com',
          person_profiles: 'identified_only' // or 'always' to create profiles for anonymous users as well
        }
      );

      posthog.capture('my event', { property: 'value' });
    }
  }).catch((e: Error) => {
    console.warn('Failed to fetch Rancher version metadata', e); // eslint-disable-line no-console
  });

}

export function init($plugin:any, store:any) {
  configureSettings($plugin, store);
  configureUiPlugins($plugin, store);
  configureManager($plugin, store);

}
