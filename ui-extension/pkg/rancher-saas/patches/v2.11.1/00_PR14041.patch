From dccc6942f0ab1d83a7f4098a6203819fbb2ef2fd Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Mon, 7 Apr 2025 14:49:49 +0100
Subject: [PATCH 1/4] Allow cluster create and import locations to be
 overridden

---
 shell/list/provisioning.cattle.io.cluster.vue | 32 ++++++++++++-------
 shell/store/type-map.js                       |  4 +++
 2 files changed, 24 insertions(+), 12 deletions(-)

diff --git a/shell/list/provisioning.cattle.io.cluster.vue b/shell/list/provisioning.cattle.io.cluster.vue
index 99fcbb0df2b..4cf45849822 100644
--- a/shell/list/provisioning.cattle.io.cluster.vue
+++ b/shell/list/provisioning.cattle.io.cluster.vue
@@ -113,24 +113,32 @@ export default {
     },
 
     createLocation() {
-      return {
-        name:   'c-cluster-product-resource-create',
-        params: {
-          product:  this.$store.getters['currentProduct'].name,
-          resource: this.resource
-        },
+      const options = this.$store.getters[`type-map/optionsFor`](this.resource)?.custom || {};
+      const params = {
+        product:  this.$store.getters['currentProduct'].name,
+        resource: this.resource
+      };
+      const defaultLocation = {
+        name: 'c-cluster-product-resource-create',
+        params
       };
+
+      return options.createLocation ? options.createLocation(params) : defaultLocation;
     },
 
     importLocation() {
-      return {
-        name:   'c-cluster-product-resource-create',
-        params: {
-          product:  this.$store.getters['currentProduct'].name,
-          resource: this.resource
-        },
+      const options = this.$store.getters[`type-map/optionsFor`](this.resource)?.custom || {};
+      const params = {
+        product:  this.$store.getters['currentProduct'].name,
+        resource: this.resource
+      };
+      const defaultLocation = {
+        name:  'c-cluster-product-resource-create',
+        params,
         query: { [MODE]: _IMPORT }
       };
+
+      return options.importLocation ? options.importLocation(params) : defaultLocation;
     },
 
     canImport() {
diff --git a/shell/store/type-map.js b/shell/store/type-map.js
index 91a19a6d036..de851c4fe7b 100644
--- a/shell/store/type-map.js
+++ b/shell/store/type-map.js
@@ -107,6 +107,7 @@
 //                               graphConfig: undefined   -- Use this to pass along the graph configuration
 //                               notFilterNamespace:  undefined -- Define namespaces that do not need to be filtered
 //                               localOnly: False -- Hide this type from the nav/search bar on downstream clusters
+//                               custom: any - Custom options for a given type
 //                           }
 // )
 // ignoreGroup(group):        Never show group or any types in it
@@ -524,6 +525,7 @@ export const getters = {
       depaginate:             false,
       customRoute:            undefined,
       resourceEditMasthead:   true,
+      custom:                 {},
     };
 
     return (schemaOrType, pagination) => {
@@ -1729,6 +1731,8 @@ export const mutations = {
     let obj = { ...options, match };
 
     if ( idx >= 0 ) {
+      // Merge the custom data object - multiple configures will update existing rather than overwrite
+      obj.custom = Object.assign(state.typeOptions[idx].custom || {}, obj.custom || {});
       obj = Object.assign(state.typeOptions[idx], obj);
       state.typeOptions.splice(idx, 1, obj);
     } else {

From 30bafb956b39f2596c152ef15ec386065ec0d95d Mon Sep 17 00:00:00 2001
From: Neil MacDougall <<EMAIL>>
Date: Mon, 7 Apr 2025 14:55:37 +0100
Subject: [PATCH 2/4] Wire into home page

---
 shell/pages/home.vue | 34 +++++++++++++++++-----------------
 1 <USER> <GROUP>, 17 insertions(+), 17 deletions(-)

diff --git a/shell/pages/home.vue b/shell/pages/home.vue
index e06f516d2c7..f3b38bcc06a 100644
--- a/shell/pages/home.vue
+++ b/shell/pages/home.vue
@@ -49,6 +49,21 @@ export default defineComponent({
   mixins: [PageHeaderActions],
 
   data() {
+    const options = this.$store.getters[`type-map/optionsFor`](CAPI.RANCHER_CLUSTER)?.custom || {};
+    const params = {
+      product:  MANAGER,
+      cluster:  BLANK_CLUSTER,
+      resource: CAPI.RANCHER_CLUSTER
+    };
+    const defaultCreateLocation = {
+      name: 'c-cluster-product-resource-create',
+      params,
+    };
+    const defaultImportLocation = {
+      ...defaultCreateLocation,
+      query: { [MODE]: _IMPORT }
+    };
+
     return {
       HIDE_HOME_PAGE_CARDS,
       fullVersion: getVersionInfo(this.$store).fullVersion,
@@ -87,24 +102,9 @@ export default defineComponent({
         },
       },
 
-      createLocation: {
-        name:   'c-cluster-product-resource-create',
-        params: {
-          product:  MANAGER,
-          cluster:  BLANK_CLUSTER,
-          resource: CAPI.RANCHER_CLUSTER
-        },
-      },
+      createLocation: options.createLocation ? options.createLocation(params) : defaultCreateLocation(params),
 
-      importLocation: {
-        name:   'c-cluster-product-resource-create',
-        params: {
-          product:  MANAGER,
-          cluster:  BLANK_CLUSTER,
-          resource: CAPI.RANCHER_CLUSTER
-        },
-        query: { [MODE]: _IMPORT }
-      },
+      importLocation: options.importLocation ? options.importLocation(params) : defaultImportLocation(params),
 
       headers: [
         STATE,

From 0c6e5d54b0d4b93961d57255fe3a11f18f216215 Mon Sep 17 00:00:00 2001
From: Neil MacDougall <<EMAIL>>
Date: Mon, 7 Apr 2025 18:18:47 +0100
Subject: [PATCH 3/4] Bug fix on home page

---
 shell/pages/home.vue | 4 ++--
 1 file changed, 2 insertions(+), 2 deletions(-)

diff --git a/shell/pages/home.vue b/shell/pages/home.vue
index f3b38bcc06a..5d7056a6604 100644
--- a/shell/pages/home.vue
+++ b/shell/pages/home.vue
@@ -102,9 +102,9 @@ export default defineComponent({
         },
       },
 
-      createLocation: options.createLocation ? options.createLocation(params) : defaultCreateLocation(params),
+      createLocation: options.createLocation ? options.createLocation(params) : defaultCreateLocation,
 
-      importLocation: options.importLocation ? options.importLocation(params) : defaultImportLocation(params),
+      importLocation: options.importLocation ? options.importLocation(params) : defaultImportLocation,
 
       headers: [
         STATE,

From c73aba2abde0b904a516c66411097a44e7b39f5c Mon Sep 17 00:00:00 2001
From: Neil MacDougall <<EMAIL>>
Date: Mon, 7 Apr 2025 18:21:48 +0100
Subject: [PATCH 4/4] Update type declaration

---
 shell/core/types.ts | 5 +++++
 1 file changed, 5 insertions(+)

diff --git a/shell/core/types.ts b/shell/core/types.ts
index a6d5379c67d..41a5027ef1f 100644
--- a/shell/core/types.ts
+++ b/shell/core/types.ts
@@ -373,6 +373,11 @@ export interface ConfigureTypeOptions {
    */
   customRoute?: Object;
 
+  /**
+   * Custom options vary pre resource type
+   */
+  custom?: any;
+
   /**
    * Leaving these here for completeness but I don't think these should be advertised as useable to plugin creators.
    */
