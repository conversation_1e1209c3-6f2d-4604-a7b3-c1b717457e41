# Rancher SaaS Extension - Developer Onboarding Guide

## Overview

The `@pkg/rancher-saas` extension is a UI plugin for Rancher Dashboard that creates a stripped-down, AWS SaaS-focused version of Rancher. Its primary goal is to provide a native EKS (Amazon Elastic Kubernetes Service) provisioning experience, removing unnecessary complexity and treating EKS as the primary and only cluster type.

## Extension Architecture

### Core Files Structure

```
pkg/rancher-saas/
├── index.ts                 # Main entry point - registers the extension
├── product.ts               # Product configuration with init functions
├── package.json             # Extension metadata and dependencies
├── vue.config.js            # Build configuration
├── config/index.js          # Constants and configuration values
├── routing/                 # Extension-specific routes
├── store/                   # Vuex store for state management
├── components/              # Vue components
├── pages/                   # Full-page Vue components
├── assets/                  # Static assets and branding
└── patches/                 # Core Rancher patches
```

### Key Components

#### 1. Extension Entry Point (`index.ts`)
- Imports and registers all component types using `importTypes()`
- Adds the product configuration from `product.ts`
- Registers custom routes and Vuex store module
- Key functions:
  - `importTypes(plugin)` - Auto-registers models, edit, detail, list components
  - `plugin.addProduct()` - Registers the extension as a product
  - `plugin.addRoutes()` - Adds extension-specific routes
  - `plugin.addDashboardStore()` - Adds Vuex store module

#### 2. Product Configuration (`product.ts`)
Contains initialization functions for different Rancher contexts:

**`configureManager()`** - Most important for SaaS functionality:
- Overrides cluster creation to default to EKS
- Hides non-EKS provisioners using `virtualType` with `ifHaveType: 'no-such-schema-so-always-hidden'`
- Customizes create/import locations for `provisioning.cattle.io.cluster`

**`configureSaasDashboard()`** - Sets up the SaaS admin product:
- Creates a new product called "saasAdmin"
- Registers virtual types for custom pages (Insights, Audit Log, Data Export, etc.)
- Sets up routing for the admin dashboard

**`configureSettings()` & `configureUiPlugins()`** - Hide default UI elements:
- Both use the hiding pattern to remove standard Rancher UI elements

**`configurePosthog()`** - Analytics integration:
- Initializes PostHog for staging environments
- Checks server URL to determine if in staging

## Extension Points and Override Mechanisms

### 1. Component Overrides
The extension can override core Rancher components by placing files in specific directories:
- `list/<resource-type>.vue` - Override list views
- `detail/<resource-type>.vue` - Override detail views
- `edit/<resource-type>.vue` - Override edit forms
- `cloud-credential/<provider>.vue` - Custom cloud credential forms

Example: `cloud-credential/buddy.vue` provides a custom AWS credential form.

### 2. Virtual Types
Used to create menu items and routes without backing Kubernetes resources:
```javascript
virtualType({
  name: 'custom-page-name',
  labelKey: 'translation.key',
  route: { /* route config */ }
})
```

### 3. Configuration Overrides
The extension modifies core behavior through:
- **Custom create/import locations**: Redirects cluster creation to EKS-specific flows
- **Hidden types**: Uses `ifHaveType: 'no-such-schema-so-always-hidden'` to hide features
- **Route modifications**: Custom routing for SaaS-specific pages

### 4. Patches
The extension includes patches to core Rancher functionality:
- `patches/v2.11.1/00_PR14041.patch` - Allows cluster create/import locations to be overridden

## Design Patterns and Mechanisms

### 1. Hiding Pattern
Frequently used to remove standard Rancher features:
```javascript
product({ ifHaveType: 'no-such-schema-so-always-hidden' });
```

### 2. DSL Pattern
Each configuration function uses a DSL pattern:
```javascript
const { product, virtualType, configureType } = $plugin.DSL(store, 'context');
```

### 3. State Management
Uses Vuex store with:
- **State**: Chat messages, configuration, AWS Q integration settings
- **Actions**: API calls, authentication flows
- **Mutations**: State updates
- **Getters**: Computed state values

### 4. Routing Structure
Routes follow the pattern: `/{product}/c/{cluster}/{resource}`
- Product: `saasAdmin`
- Cluster: `_` (blank cluster for global resources)
- Resource: Custom page names

## Development Workflow

### 1. Adding a New Page
1. Create Vue component in `pages/`
2. Add route in `routing/extension-routing.js`
3. Register as virtualType in `product.ts`
4. Add to basicType array for menu visibility

### 2. Modifying EKS Behavior
1. Use `configureType()` in `configureManager()` to override provisioning.cattle.io.cluster
2. Set custom createLocation/importLocation to redirect to EKS
3. Hide other provisioners using virtualType pattern

### 3. Adding Store Functionality
1. Add state properties in `store/index.ts`
2. Create actions in `store/actions.ts`
3. Add mutations in `store/mutations.ts`
4. Create getters in `store/getters.ts`

### 4. Overriding Core Components
1. Create component in appropriate directory structure
2. Match the props/events interface of core component
3. Test thoroughly as these create tight coupling

## Key Features Implementation

### AWS Q Integration
- Store contains Q Business configuration
- Authentication flow through `/qindex/authenticate`
- Chat functionality with message history
- Backend proxy through Rancher's meta proxy

### Custom Branding
- Assets in `assets/brand/suse/`
- Metadata.json indicates custom stylesheet support
- Logo replacements for dark/light themes

### Analytics
- PostHog integration for staging environments
- Automatic initialization based on server URL
- Event tracking capabilities

## Best Practices

1. **Prefer Additive Enhancement**: Use plugin methods like `addAction()`, `addTab()` over component overrides
2. **Maintain Simplicity**: Keep changes minimal and focused on EKS/SaaS requirements
3. **Use Extension Patterns**: Follow established patterns for hiding, routing, and configuration
4. **Test Upgrades**: Component overrides may break with Rancher updates
5. **Document Changes**: Keep track of all overrides and customizations

## Common Tasks

### Hide a Feature
```javascript
virtualType({
  name: 'feature-name',
  ifHaveType: 'no-such-schema-so-always-hidden'
});
```

### Add a Menu Item
```javascript
virtualType({
  labelKey: 'translation.key',
  name: 'menu-item-name',
  route: { /* route config */ }
});
basicType(['menu-item-name']);
```

### Override Cluster Creation
```javascript
configureType('provisioning.cattle.io.cluster', {
  custom: {
    createLocation: (params) => ({
      name: 'custom-route',
      query: { type: 'amazoneks' },
      params
    })
  }
});
```

## Debugging Tips

1. Check browser console for extension loading messages
2. Verify routes are registered in Vue Router
3. Use Vue DevTools to inspect component hierarchy
4. Check Vuex store for state management issues
5. Verify patches are applied correctly

## Next Steps

1. Explore the existing pages in `pages/` directory
2. Review the store implementation for state management patterns
3. Test the extension in a local Rancher environment
4. Familiarize yourself with the EKS provisioning flow
5. Review the shell directory documentation for core Rancher concepts

Remember: The goal is to create a streamlined, EKS-focused experience while maintaining compatibility with Rancher's extension system.